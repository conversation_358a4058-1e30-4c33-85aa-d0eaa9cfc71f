<script setup lang="ts">
import { Head, Link, router, useForm } from '@inertiajs/vue3'
import { computed, ref, toRefs, watch } from 'vue'
import { debounce } from 'lodash-es'
import AppLayout from '@/layouts/AppLayout.vue'
import Button from '@/components/ui/button/Button.vue'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { AlertCircle, Eye, FileCheck, X } from 'lucide-vue-next'
import Pagination from '@/components/Pagination.vue'
import TextLink from '@/components/TextLink.vue'

// Types
interface Pendaftaran {
  id_pendaftaran: number
  nomor_pendaftaran: string
  created_at: string
  peserta: {
    nama_lengkap: string
    nik: string
    wilayah: {
      nama_wilayah: string
    }
  }
  golongan: {
    nama_golongan: string
  }
  verifikasi_pendaftaran?: {
    status_nik: string
    status_dokumen: string
    status_verifikasi: string
    catatan_nik?: string
    catatan_dokumen?: string
    catatan_verifikasi?: string
  }
}

interface PaginatedData {
  data: Pendaftaran[]
  from: number
  to: number
  total: number
  prev_page_url: string | null
  next_page_url: string | null
  links: Array<{
    url: string | null
    label: string
    active: boolean
  }>
}

interface Wilayah {
  id_wilayah: number
  nama_wilayah: string
}

interface Golongan {
  id_golongan: number
  nama_golongan: string
}

interface Filters {
  status_verifikasi: string
  wilayah: string
  golongan: string
  search: string
}

// Props
const props = defineProps<{
  pendaftaran: PaginatedData
  filters: Filters
  wilayah: Wilayah[]
  golongan: Golongan[]
}>()

// Destructure reactive props
const { pendaftaran, filters: initialFilters, wilayah, golongan } = toRefs(props)

// State
const showVerificationModal = ref(false)
const selectedPendaftaran = ref<Pendaftaran | null>(null)
const filters = ref<Filters>({ ...initialFilters.value })

// Computed
const statusBadgeClass = computed(() => {
  return (status: string, type: 'nik' | 'dokumen' | 'verifikasi') => {
    const baseClass = 'inline-flex px-2 py-1 text-xs font-semibold rounded-full'

    switch (status) {
      case 'sesuai':
      case 'berkas_sesuai':
      case 'approved':
        return `${baseClass} bg-green-100 text-green-800`
      case 'tidak_sesuai':
      case 'berkas_tidak_sesuai':
      case 'rejected':
        return `${baseClass} bg-red-100 text-red-800`
      case 'pending':
        return `${baseClass} bg-yellow-100 text-yellow-800`
      default:
        return `${baseClass} bg-gray-100 text-gray-800`
    }
  }
})

const statusText = computed(() => {
  return (status: string, type: 'nik' | 'dokumen' | 'verifikasi') => {
    const statusMap = {
      nik: {
        sesuai: 'Sesuai',
        tidak_sesuai: 'Tidak Sesuai',
        pending: 'Pending'
      },
      dokumen: {
        berkas_sesuai: 'Berkas Sesuai',
        berkas_tidak_sesuai: 'Berkas Tidak Sesuai',
        pending: 'Pending'
      },
      verifikasi: {
        approved: 'Disetujui',
        rejected: 'Ditolak',
        pending: 'Menunggu Verifikasi'
      }
    }

    return statusMap[type][status] || 'Belum Diverifikasi'
  }
})

const canVerify = computed(() => {
  return (pendaftaran: Pendaftaran) => {
    return !pendaftaran.verifikasi_pendaftaran ||
           pendaftaran.verifikasi_pendaftaran.status_verifikasi === 'pending'
  }
})

// Forms
const verificationForm = useForm({
  status_nik: '',
  catatan_nik: '',
  status_dokumen: '',
  catatan_dokumen: '',
  catatan_verifikasi: ''
})

// Debounced search
const debouncedApplyFilters = debounce(applyFilters, 300)

// Methods
function applyFilters() {
  router.get(route('admin.verifikasi-provinsi.index'), filters.value, {
    preserveState: true,
    preserveScroll: true,
    replace: true
  })
}

function resetFilters() {
  filters.value = {
    status_verifikasi: '',
    wilayah: '',
    golongan: '',
    search: ''
  }
  applyFilters()
}

function openVerificationModal(pendaftaran: Pendaftaran) {
  selectedPendaftaran.value = pendaftaran

  // Pre-fill form if verification exists
  const verification = pendaftaran.verifikasi_pendaftaran
  if (verification) {
    verificationForm.status_nik = verification.status_nik
    verificationForm.catatan_nik = verification.catatan_nik || ''
    verificationForm.status_dokumen = verification.status_dokumen
    verificationForm.catatan_dokumen = verification.catatan_dokumen || ''
    verificationForm.catatan_verifikasi = verification.catatan_verifikasi || ''
  } else {
    verificationForm.reset()
  }

  showVerificationModal.value = true
}

function closeVerificationModal() {
  showVerificationModal.value = false
  selectedPendaftaran.value = null
  verificationForm.reset()
  verificationForm.clearErrors()
}

function submitVerification() {
  if (!selectedPendaftaran.value) return

  verificationForm.post(route('admin.verifikasi-provinsi.verify', selectedPendaftaran.value.id_pendaftaran), {
    onSuccess: () => {
      closeVerificationModal()
      router.reload({ only: ['pendaftaran'] })
    },
    onError: (errors) => {
      console.error('Verification failed:', errors)
    }
  })
}

function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('id-ID', {
    day: 'numeric',
    month: 'long',
    year: 'numeric'
  })
}

// Watch for search changes
watch(() => filters.value.search, debouncedApplyFilters)

// Watch for other filter changes
watch([
  () => filters.value.status_verifikasi,
  () => filters.value.wilayah,
  () => filters.value.golongan
], applyFilters)
</script>

<template>
  <AppLayout>
    <Head title="Verifikasi Pendaftaran - Admin Provinsi" />

    <div class="space-y-6">
      <!-- Header -->
      <Card>
        <CardHeader>
          <CardTitle class="text-2xl">Verifikasi Pendaftaran</CardTitle>
          <p class="text-muted-foreground">Verifikasi NIK dan dokumen peserta MTQ</p>
        </CardHeader>
      </Card>

      <!-- Filters -->
      <Card>
        <CardContent class="pt-6">
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="space-y-2">
              <Label for="status-filter">Status Verifikasi</Label>
              <Select v-model="filters.status_verifikasi">
                <SelectTrigger id="status-filter">
                  <SelectValue placeholder="Semua Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Status</SelectItem>
                  <SelectItem value="pending">Menunggu Verifikasi</SelectItem>
                  <SelectItem value="approved">Disetujui</SelectItem>
                  <SelectItem value="rejected">Ditolak</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div class="space-y-2">
              <Label for="wilayah-filter">Wilayah</Label>
              <Select v-model="filters.wilayah">
                <SelectTrigger id="wilayah-filter">
                  <SelectValue placeholder="Semua Wilayah" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Wilayah</SelectItem>
                  <SelectItem
                    v-for="w in wilayah"
                    :key="w.id_wilayah"
                    :value="w.id_wilayah.toString()"
                  >
                    {{ w.nama_wilayah }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div class="space-y-2">
              <Label for="golongan-filter">Golongan</Label>
              <Select v-model="filters.golongan">
                <SelectTrigger id="golongan-filter">
                  <SelectValue placeholder="Semua Golongan" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Golongan</SelectItem>
                  <SelectItem
                    v-for="g in golongan"
                    :key="g.id_golongan"
                    :value="g.id_golongan.toString()"
                  >
                    {{ g.nama_golongan }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div class="space-y-2">
              <Label for="search-filter">Pencarian</Label>
              <Input
                id="search-filter"
                v-model="filters.search"
                placeholder="Nama, NIK, atau nomor pendaftaran..."
                type="search"
              />
            </div>
          </div>

          <div class="flex justify-end gap-2 mt-4">
            <Button variant="outline" @click="resetFilters">
              Reset
            </Button>
            <Button @click="applyFilters">
              Terapkan Filter
            </Button>
          </div>
        </CardContent>
      </Card>

      <!-- Registrations Table -->
      <Card>
        <div class="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Peserta</TableHead>
                <TableHead>Wilayah</TableHead>
                <TableHead>Golongan</TableHead>
                <TableHead>Status NIK</TableHead>
                <TableHead>Status Dokumen</TableHead>
                <TableHead>Status Verifikasi</TableHead>
                <TableHead>Tanggal</TableHead>
                <TableHead>Aksi</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-for="item in pendaftaran.data" :key="item.id_pendaftaran">
                <TableCell>
                  <div class="space-y-1">
                    <div class="font-medium">{{ item.peserta.nama_lengkap }}</div>
                    <div class="text-sm text-muted-foreground">{{ item.peserta.nik }}</div>
                    <div class="text-sm text-muted-foreground">{{ item.nomor_pendaftaran }}</div>
                  </div>
                </TableCell>
                <TableCell>{{ item.peserta.wilayah.nama_wilayah }}</TableCell>
                <TableCell>{{ item.golongan.nama_golongan }}</TableCell>
                <TableCell>
                  <Badge
                    :class="statusBadgeClass(item.verifikasi_pendaftaran?.status_nik || 'pending', 'nik')"
                    variant="secondary"
                  >
                    {{ statusText(item.verifikasi_pendaftaran?.status_nik || 'pending', 'nik') }}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge
                    :class="statusBadgeClass(item.verifikasi_pendaftaran?.status_dokumen || 'pending', 'dokumen')"
                    variant="secondary"
                  >
                    {{ statusText(item.verifikasi_pendaftaran?.status_dokumen || 'pending', 'dokumen') }}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge
                    :class="statusBadgeClass(item.verifikasi_pendaftaran?.status_verifikasi || 'pending', 'verifikasi')"
                    variant="secondary"
                  >
                    {{ statusText(item.verifikasi_pendaftaran?.status_verifikasi || 'pending', 'verifikasi') }}
                  </Badge>
                </TableCell>
                <TableCell>{{ formatDate(item.created_at) }}</TableCell>
                <TableCell>
                  <div class="flex gap-2">
                    <Button variant="ghost" size="sm" asChild>
                      <TextLink :href="route('admin.verifikasi-provinsi.show', item.id_pendaftaran)">
                        <Eye class="h-4 w-4 mr-1" />
                        Detail
                      </TextLink>
                    </Button>
                    <Button
                      v-if="canVerify(item)"
                      variant="ghost"
                      size="sm"
                      @click="openVerificationModal(item)"
                    >
                      <FileCheck class="h-4 w-4 mr-1" />
                      Verifikasi
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>

        <!-- Pagination -->
        <div class="border-t p-4">
          <div class="flex items-center justify-between">
            <div class="text-sm text-muted-foreground">
              Menampilkan {{ pendaftaran.from }} hingga {{ pendaftaran.to }} dari {{ pendaftaran.total }} hasil
            </div>
            <Pagination :links="pendaftaran.links" />
          </div>
        </div>
      </Card>

      <!-- Verification Modal -->
      <Dialog v-model:open="showVerificationModal">
        <DialogContent class="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Verifikasi Pendaftaran</DialogTitle>
            <DialogDescription>
              Lakukan verifikasi NIK dan dokumen peserta
            </DialogDescription>
          </DialogHeader>

          <div v-if="selectedPendaftaran" class="space-y-6">
            <!-- Participant Info -->
            <Card>
              <CardHeader>
                <CardTitle class="text-lg">Informasi Peserta</CardTitle>
              </CardHeader>
              <CardContent>
                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <Label class="text-muted-foreground">Nama</Label>
                    <p class="font-medium">{{ selectedPendaftaran.peserta.nama_lengkap }}</p>
                  </div>
                  <div>
                    <Label class="text-muted-foreground">NIK</Label>
                    <p class="font-medium">{{ selectedPendaftaran.peserta.nik }}</p>
                  </div>
                  <div>
                    <Label class="text-muted-foreground">Wilayah</Label>
                    <p class="font-medium">{{ selectedPendaftaran.peserta.wilayah.nama_wilayah }}</p>
                  </div>
                  <div>
                    <Label class="text-muted-foreground">Golongan</Label>
                    <p class="font-medium">{{ selectedPendaftaran.golongan.nama_golongan }}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- Verification Form -->
            <form @submit.prevent="submitVerification" class="space-y-6">
              <!-- NIK Verification -->
              <div class="space-y-3">
                <Label class="text-base font-medium">Verifikasi NIK</Label>
                <RadioGroup v-model="verificationForm.status_nik" class="space-y-2">
                  <div class="flex items-center space-x-2">
                    <RadioGroupItem value="sesuai" id="nik-sesuai" />
                    <Label for="nik-sesuai">Sesuai</Label>
                  </div>
                  <div class="flex items-center space-x-2">
                    <RadioGroupItem value="tidak_sesuai" id="nik-tidak-sesuai" />
                    <Label for="nik-tidak-sesuai">Tidak Sesuai</Label>
                  </div>
                </RadioGroup>
                <Textarea
                  v-model="verificationForm.catatan_nik"
                  placeholder="Catatan verifikasi NIK (opsional)"
                  rows="2"
                />
              </div>

              <!-- Document Verification -->
              <div class="space-y-3">
                <Label class="text-base font-medium">Verifikasi Dokumen</Label>
                <RadioGroup v-model="verificationForm.status_dokumen" class="space-y-2">
                  <div class="flex items-center space-x-2">
                    <RadioGroupItem value="berkas_sesuai" id="dok-sesuai" />
                    <Label for="dok-sesuai">Berkas Sesuai</Label>
                  </div>
                  <div class="flex items-center space-x-2">
                    <RadioGroupItem value="berkas_tidak_sesuai" id="dok-tidak-sesuai" />
                    <Label for="dok-tidak-sesuai">Berkas Tidak Sesuai</Label>
                  </div>
                </RadioGroup>
                <Textarea
                  v-model="verificationForm.catatan_dokumen"
                  placeholder="Catatan verifikasi dokumen (opsional)"
                  rows="2"
                />
              </div>

              <!-- General Notes -->
              <div class="space-y-3">
                <Label for="catatan-umum" class="text-base font-medium">Catatan Verifikasi</Label>
                <Textarea
                  id="catatan-umum"
                  v-model="verificationForm.catatan_verifikasi"
                  placeholder="Catatan umum verifikasi (opsional)"
                  rows="3"
                />
              </div>

              <DialogFooter>
                <Button type="button" variant="outline" @click="closeVerificationModal">
                  Batal
                </Button>
                <Button
                  type="submit"
                  :disabled="!verificationForm.status_nik || !verificationForm.status_dokumen || verificationForm.processing"
                >
                  <FileCheck class="h-4 w-4 mr-2" />
                  {{ verificationForm.processing ? 'Memproses...' : 'Simpan Verifikasi' }}
                </Button>
              </DialogFooter>
            </form>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  </AppLayout>
</template>
