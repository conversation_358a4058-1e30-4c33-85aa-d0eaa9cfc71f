<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CreateTestData extends Command
{
    protected $signature = 'test:create-data';
    protected $description = 'Create test data for upload testing';

    public function handle()
    {
        $this->info('Creating test data...');

        try {
            // Get first peserta
            $peserta = DB::table('peserta')->first();
            if (!$peserta) {
                $this->error('No peserta found');
                return;
            }

            $this->info('Using peserta: ' . $peserta->nama_lengkap);

            // Check if peserta already has pendaftaran
            $existingPendaftaran = DB::table('pendaftaran')
                ->where('id_peserta', $peserta->id_peserta)
                ->first();

            if ($existingPendaftaran) {
                $this->info('Peserta already has pendaftaran: ' . $existingPendaftaran->nomor_pendaftaran);
                return;
            }

            // Get first golongan
            $golongan = DB::table('golongan')->first();
            if (!$golongan) {
                $this->error('No golongan found');
                return;
            }

            $this->info('Using golongan: ' . $golongan->nama_golongan);

            // Create pendaftaran
            $pendaftaranId = DB::table('pendaftaran')->insertGetId([
                'id_peserta' => $peserta->id_peserta,
                'id_golongan' => $golongan->id_golongan,
                'nomor_pendaftaran' => 'TEST-' . time(),
                'nomor_peserta' => 'P-' . str_pad($peserta->id_peserta, 4, '0', STR_PAD_LEFT),
                'nomor_urut' => 1,
                'tahun_pendaftaran' => date('Y'),
                'status_pendaftaran' => 'approved',
                'tanggal_daftar' => now(),
                'registered_by' => 1,
                'keterangan' => 'Test pendaftaran for upload testing',
                'created_at' => now(),
                'updated_at' => now()
            ]);

            $this->info('Pendaftaran created successfully!');
            $this->info('Pendaftaran ID: ' . $pendaftaranId);

        } catch (\Exception $e) {
            $this->error('Error: ' . $e->getMessage());
        }
    }
}
