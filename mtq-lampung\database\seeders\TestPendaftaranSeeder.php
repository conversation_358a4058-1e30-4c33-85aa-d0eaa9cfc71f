<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Peserta;
use App\Models\Pendaftaran;
use App\Models\Golongan;

class TestPendaftaranSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get first peserta
        $peserta = Peserta::first();
        if (!$peserta) {
            echo "No peserta found\n";
            return;
        }

        echo "Using peserta: " . $peserta->nama_lengkap . "\n";

        // Check if peserta already has pendaftaran
        if ($peserta->pendaftaran->isNotEmpty()) {
            echo "Peserta already has pendaftaran\n";
            return;
        }

        // Get first golongan
        $golongan = Golongan::first();
        if (!$golongan) {
            echo "No golongan found\n";
            return;
        }

        echo "Using golongan: " . $golongan->nama_golongan . "\n";

        // Create pendaftaran
        $pendaftaran = Pendaftaran::create([
            'id_peserta' => $peserta->id_peserta,
            'id_golongan' => $golongan->id_golongan,
            'nomor_pendaftaran' => 'TEST-' . time(),
            'nomor_peserta' => 'P-' . str_pad($peserta->id_peserta, 4, '0', STR_PAD_LEFT),
            'nomor_urut' => 1,
            'tahun_pendaftaran' => date('Y'),
            'status_pendaftaran' => 'approved',
            'tanggal_daftar' => now(),
            'registered_by' => 1, // Assuming admin user ID 1
            'keterangan' => 'Test pendaftaran for upload testing'
        ]);

        echo "Pendaftaran created successfully!\n";
        echo "Pendaftaran ID: " . $pendaftaran->id_pendaftaran . "\n";
        echo "Nomor Pendaftaran: " . $pendaftaran->nomor_pendaftaran . "\n";
    }
}
