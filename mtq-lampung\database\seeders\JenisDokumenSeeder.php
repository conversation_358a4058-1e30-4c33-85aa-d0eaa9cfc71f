<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\JenisDokumen;

class JenisDokumenSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $jenisDokumen = [
            [
                'kode_dokumen' => 'FOTO',
                'nama_dokumen' => 'Foto Peserta',
                'deskripsi' => 'Foto formal peserta dengan latar belakang putih',
                'kategori' => 'identitas',
                'wajib' => true,
                'format_file' => 'jpg,jpeg,png',
                'ukuran_max_kb' => 1024,
                'status' => 'aktif',
                'urutan' => 1
            ],
            [
                'kode_dokumen' => 'KTP',
                'nama_dokumen' => 'Kartu <PERSON> Penduduk (KTP)',
                'deskripsi' => 'Scan KTP yang masih berlaku',
                'kategori' => 'identitas',
                'wajib' => true,
                'format_file' => 'jpg,jpeg,png,pdf',
                'ukuran_max_kb' => 2048,
                'status' => 'aktif',
                'urutan' => 2
            ],
            [
                'kode_dokumen' => 'KK',
                'nama_dokumen' => 'Kartu Keluarga',
                'deskripsi' => 'Scan Kartu Keluarga yang masih berlaku',
                'kategori' => 'identitas',
                'wajib' => true,
                'format_file' => 'jpg,jpeg,png,pdf',
                'ukuran_max_kb' => 2048,
                'status' => 'aktif',
                'urutan' => 3
            ],
            [
                'kode_dokumen' => 'REKOMENDASI',
                'nama_dokumen' => 'Surat Rekomendasi',
                'deskripsi' => 'Surat rekomendasi dari instansi/organisasi terkait',
                'kategori' => 'rekomendasi',
                'wajib' => true,
                'format_file' => 'pdf',
                'ukuran_max_kb' => 3072,
                'status' => 'aktif',
                'urutan' => 4
            ],
            [
                'kode_dokumen' => 'IJAZAH',
                'nama_dokumen' => 'Ijazah Terakhir',
                'deskripsi' => 'Scan ijazah pendidikan terakhir',
                'kategori' => 'pendidikan',
                'wajib' => false,
                'format_file' => 'jpg,jpeg,png,pdf',
                'ukuran_max_kb' => 2048,
                'status' => 'aktif',
                'urutan' => 5
            ],
            [
                'kode_dokumen' => 'SERTIFIKAT',
                'nama_dokumen' => 'Sertifikat Prestasi',
                'deskripsi' => 'Sertifikat prestasi di bidang MTQ (jika ada)',
                'kategori' => 'lainnya',
                'wajib' => false,
                'format_file' => 'jpg,jpeg,png,pdf',
                'ukuran_max_kb' => 2048,
                'status' => 'aktif',
                'urutan' => 6
            ]
        ];

        foreach ($jenisDokumen as $dokumen) {
            JenisDokumen::updateOrCreate(
                ['kode_dokumen' => $dokumen['kode_dokumen']],
                $dokumen
            );
        }
    }
}
