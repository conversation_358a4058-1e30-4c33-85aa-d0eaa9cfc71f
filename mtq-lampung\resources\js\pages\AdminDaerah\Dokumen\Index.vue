<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue'
import { Head, router, useForm } from '@inertiajs/vue3'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Checkbox } from '@/components/ui/checkbox'
import Icon from '@/components/Icon.vue'
import TextLink from '@/components/TextLink.vue'
import { ref, computed } from 'vue'

interface Wilayah {
  nama_wilayah: string
}

interface User {
  nama_lengkap: string
}

interface Peserta {
  nama_lengkap: string
  wilayah: Wilayah
  user: User
}

interface CabangLomba {
  nama_cabang: string
}

interface Golongan {
  nama_golongan: string
  cabang_lomba: CabangLomba
}

interface Pendaftaran {
  nomor_pendaftaran: string
  peserta: Peserta
  golongan: Golongan
}

interface DokumenPeserta {
  id_dokumen: number
  jenis_dokumen: string
  nama_file: string
  ukuran_file: number
  status_verifikasi: string
  catatan_verifikasi: string | null
  verified_at: string | null
  created_at: string
  pendaftaran: Pendaftaran
  uploaded_by: User
  verified_by: User | null
}

interface PaginatedData {
  data: DokumenPeserta[]
  current_page: number
  last_page: number
  per_page: number
  total: number
  links: Array<{
    url: string | null
    label: string
    active: boolean
  }>
}

const props = defineProps<{
  dokumen: PaginatedData
  filters: {
    status?: string
    jenis_dokumen?: string
    search?: string
  }
  jenisOptions: Record<string, string>
}>()

const searchForm = useForm({
  search: props.filters.search || '',
  status: props.filters.status || '',
  jenis_dokumen: props.filters.jenis_dokumen || ''
})

const verifyForm = useForm({
  status_verifikasi: '',
  catatan_verifikasi: '',
  kualitas_gambar: null as boolean | null,
  kelengkapan_data: null as boolean | null,
  kejelasan_teks: null as boolean | null
})

const bulkForm = useForm({
  dokumen_ids: [] as number[],
  status_verifikasi: '',
  catatan_verifikasi: ''
})

const selectedDokumen = ref<number[]>([])
const showVerifyDialog = ref(false)
const showBulkDialog = ref(false)
const selectedDocument = ref<DokumenPeserta | null>(null)

const allSelected = computed(() => {
  return props.dokumen.data.length > 0 && selectedDokumen.value.length === props.dokumen.data.length
})

function search() {
  searchForm.get(route('admin-daerah.dokumen.index'), {
    preserveState: true,
    replace: true
  })
}

function clearFilters() {
  searchForm.reset()
  search()
}

function toggleAll() {
  if (allSelected.value) {
    selectedDokumen.value = []
  } else {
    selectedDokumen.value = props.dokumen.data.map(item => item.id_dokumen)
  }
}

function openVerifyDialog(dokumen: DokumenPeserta) {
  selectedDocument.value = dokumen
  verifyForm.reset()
  showVerifyDialog.value = true
}

function verifyDocument() {
  if (!selectedDocument.value) return

  verifyForm.post(route('admin-daerah.dokumen.verify', selectedDocument.value.id_dokumen), {
    onSuccess: () => {
      showVerifyDialog.value = false
      selectedDocument.value = null
      verifyForm.reset()
    }
  })
}

function openBulkDialog() {
  if (selectedDokumen.value.length === 0) {
    alert('Pilih dokumen yang akan diverifikasi')
    return
  }

  bulkForm.dokumen_ids = selectedDokumen.value
  bulkForm.reset('status_verifikasi', 'catatan_verifikasi')
  showBulkDialog.value = true
}

function bulkVerify() {
  bulkForm.post(route('admin-daerah.dokumen.bulk-verify'), {
    onSuccess: () => {
      showBulkDialog.value = false
      selectedDokumen.value = []
      bulkForm.reset()
    }
  })
}

function getStatusBadgeVariant(status: string) {
  const variants: Record<string, string> = {
    'pending': 'secondary',
    'approved': 'default',
    'rejected': 'destructive'
  }
  return variants[status] || 'secondary'
}

function getStatusLabel(status: string) {
  const labels: Record<string, string> = {
    'pending': 'Menunggu',
    'approved': 'Disetujui',
    'rejected': 'Ditolak'
  }
  return labels[status] || status
}

function formatFileSize(sizeBytes: number): string {
  const sizeKb = sizeBytes / 1024
  if (sizeKb >= 1024) {
    return `${(sizeKb / 1024).toFixed(1)} MB`
  }
  return `${sizeKb.toFixed(1)} KB`
}

function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<template>
  <AppLayout>
    <Head title="Manajemen Dokumen" />

    <div class="space-y-6">
      <!-- Header -->
      <div class="flex items-center justify-between">
        <div class="islamic-gradient p-6 rounded-lg islamic-shadow">
          <Heading title="Manajemen Dokumen" class="text-white"/>
          <p class="text-green-100 mt-2">Kelola dan verifikasi dokumen peserta MTQ di wilayah Anda</p>
        </div>
        <div class="flex gap-2">
          <Button 
            variant="outline" 
            @click="openBulkDialog"
            :disabled="selectedDokumen.length === 0"
            class="islamic-shadow"
          >
            <Icon name="check-square" class="w-4 h-4 mr-2" />
            Verifikasi Massal ({{ selectedDokumen.length }})
          </Button>
        </div>
      </div>

      <!-- Filters -->
      <Card class="islamic-shadow">
        <CardHeader>
          <CardTitle class="text-islamic-700">Filter & Pencarian</CardTitle>
        </CardHeader>
        <CardContent>
          <form @submit.prevent="search" class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label for="search">Pencarian</Label>
              <Input
                id="search"
                v-model="searchForm.search"
                placeholder="Cari nama peserta, nomor pendaftaran, atau nama file..."
                class="mt-1"
              />
            </div>
            <div>
              <Label for="status">Status Verifikasi</Label>
              <Select v-model="searchForm.status">
                <SelectTrigger class="mt-1">
                  <SelectValue placeholder="Semua Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Semua Status</SelectItem>
                  <SelectItem value="pending">Menunggu</SelectItem>
                  <SelectItem value="approved">Disetujui</SelectItem>
                  <SelectItem value="rejected">Ditolak</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label for="jenis_dokumen">Jenis Dokumen</Label>
              <Select v-model="searchForm.jenis_dokumen">
                <SelectTrigger class="mt-1">
                  <SelectValue placeholder="Semua Jenis" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Semua Jenis</SelectItem>
                  <SelectItem 
                    v-for="(label, value) in jenisOptions" 
                    :key="value" 
                    :value="value"
                  >
                    {{ label }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div class="flex items-end gap-2">
              <Button type="submit" :disabled="searchForm.processing">
                <Icon name="search" class="w-4 h-4 mr-2" />
                Cari
              </Button>
              <Button type="button" variant="outline" @click="clearFilters">
                <Icon name="x" class="w-4 h-4 mr-2" />
                Reset
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      <!-- Data Table -->
      <Card class="islamic-shadow">
        <CardHeader>
          <CardTitle class="text-islamic-700">Daftar Dokumen</CardTitle>
          <CardDescription>
            Total: {{ dokumen.total }} dokumen
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div class="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead class="w-12">
                    <Checkbox 
                      :checked="allSelected"
                      @update:checked="toggleAll"
                    />
                  </TableHead>
                  <TableHead>Peserta</TableHead>
                  <TableHead>Jenis Dokumen</TableHead>
                  <TableHead>File</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Diverifikasi</TableHead>
                  <TableHead>Tanggal Upload</TableHead>
                  <TableHead class="text-right">Aksi</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow v-for="item in dokumen.data" :key="item.id_dokumen">
                  <TableCell>
                    <Checkbox 
                      :checked="selectedDokumen.includes(item.id_dokumen)"
                      @update:checked="(checked) => {
                        if (checked) {
                          selectedDokumen.push(item.id_dokumen)
                        } else {
                          const index = selectedDokumen.indexOf(item.id_dokumen)
                          if (index > -1) selectedDokumen.splice(index, 1)
                        }
                      }"
                    />
                  </TableCell>
                  <TableCell>
                    <div>
                      <div class="font-medium">{{ item.pendaftaran.peserta.nama_lengkap }}</div>
                      <div class="text-sm text-muted-foreground">
                        {{ item.pendaftaran.nomor_pendaftaran }}
                      </div>
                      <div class="text-sm text-muted-foreground">
                        {{ item.pendaftaran.golongan.nama_golongan }} - {{ item.pendaftaran.golongan.cabang_lomba.nama_cabang }}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{{ item.jenis_dokumen }}</Badge>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div class="font-medium">{{ item.nama_file }}</div>
                      <div class="text-sm text-muted-foreground">
                        {{ formatFileSize(item.ukuran_file) }}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge :variant="getStatusBadgeVariant(item.status_verifikasi)">
                      {{ getStatusLabel(item.status_verifikasi) }}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div v-if="item.verified_by">
                      <div class="text-sm">{{ item.verified_by.nama_lengkap }}</div>
                      <div class="text-xs text-muted-foreground">
                        {{ formatDate(item.verified_at!) }}
                      </div>
                    </div>
                    <span v-else class="text-muted-foreground">-</span>
                  </TableCell>
                  <TableCell>{{ formatDate(item.created_at) }}</TableCell>
                  <TableCell class="text-right">
                    <div class="flex items-center justify-end gap-2">
                      <Button variant="outline" size="sm" as-child>
                        <TextLink :href="route('admin-daerah.dokumen.show', item.id_dokumen)">
                          <Icon name="eye" class="w-4 h-4" />
                        </TextLink>
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        @click="openVerifyDialog(item)"
                        v-if="item.status_verifikasi === 'pending'"
                      >
                        <Icon name="check" class="w-4 h-4" />
                      </Button>
                      <Button variant="outline" size="sm" as-child>
                        <a :href="route('admin-daerah.dokumen.download', item.id_dokumen)" target="_blank">
                          <Icon name="download" class="w-4 h-4" />
                        </a>
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>

          <!-- Pagination -->
          <div v-if="dokumen.last_page > 1" class="mt-4 flex justify-center">
            <div class="flex items-center gap-2">
              <Button
                v-for="link in dokumen.links"
                :key="link.label"
                :variant="link.active ? 'default' : 'outline'"
                size="sm"
                :disabled="!link.url"
                @click="link.url && router.visit(link.url)"
                v-html="link.label"
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- Verify Dialog -->
    <Dialog v-model:open="showVerifyDialog">
      <DialogContent class="max-w-md">
        <DialogHeader>
          <DialogTitle>Verifikasi Dokumen</DialogTitle>
          <DialogDescription>
            Verifikasi dokumen "{{ selectedDocument?.jenis_dokumen }}" dari {{ selectedDocument?.pendaftaran.peserta.nama_lengkap }}
          </DialogDescription>
        </DialogHeader>
        <form @submit.prevent="verifyDocument" class="space-y-4">
          <div>
            <Label for="status_verifikasi">Status Verifikasi *</Label>
            <Select v-model="verifyForm.status_verifikasi" required>
              <SelectTrigger class="mt-1">
                <SelectValue placeholder="Pilih status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="sesuai">Sesuai</SelectItem>
                <SelectItem value="tidak_sesuai">Tidak Sesuai</SelectItem>
                <SelectItem value="perlu_perbaikan">Perlu Perbaikan</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label for="catatan_verifikasi">Catatan</Label>
            <Input
              id="catatan_verifikasi"
              v-model="verifyForm.catatan_verifikasi"
              placeholder="Catatan verifikasi (opsional)"
              class="mt-1"
            />
          </div>

          <div class="space-y-2">
            <Label>Kriteria Verifikasi</Label>
            <div class="space-y-2">
              <div class="flex items-center space-x-2">
                <Checkbox v-model:checked="verifyForm.kualitas_gambar" />
                <Label class="text-sm">Kualitas gambar baik</Label>
              </div>
              <div class="flex items-center space-x-2">
                <Checkbox v-model:checked="verifyForm.kelengkapan_data" />
                <Label class="text-sm">Data lengkap</Label>
              </div>
              <div class="flex items-center space-x-2">
                <Checkbox v-model:checked="verifyForm.kejelasan_teks" />
                <Label class="text-sm">Teks jelas terbaca</Label>
              </div>
            </div>
          </div>

          <div class="flex justify-end gap-2 pt-4">
            <Button variant="outline" type="button" @click="showVerifyDialog = false">
              Batal
            </Button>
            <Button type="submit" :disabled="verifyForm.processing">
              <Icon name="check" class="w-4 h-4 mr-2" />
              {{ verifyForm.processing ? 'Memverifikasi...' : 'Verifikasi' }}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>

    <!-- Bulk Verify Dialog -->
    <Dialog v-model:open="showBulkDialog">
      <DialogContent class="max-w-md">
        <DialogHeader>
          <DialogTitle>Verifikasi Massal</DialogTitle>
          <DialogDescription>
            Verifikasi {{ selectedDokumen.length }} dokumen sekaligus
          </DialogDescription>
        </DialogHeader>
        <form @submit.prevent="bulkVerify" class="space-y-4">
          <div>
            <Label for="bulk_status">Status Verifikasi *</Label>
            <Select v-model="bulkForm.status_verifikasi" required>
              <SelectTrigger class="mt-1">
                <SelectValue placeholder="Pilih status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="sesuai">Sesuai</SelectItem>
                <SelectItem value="tidak_sesuai">Tidak Sesuai</SelectItem>
                <SelectItem value="perlu_perbaikan">Perlu Perbaikan</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label for="bulk_catatan">Catatan</Label>
            <Input
              id="bulk_catatan"
              v-model="bulkForm.catatan_verifikasi"
              placeholder="Catatan verifikasi (opsional)"
              class="mt-1"
            />
          </div>

          <div class="flex justify-end gap-2 pt-4">
            <Button variant="outline" type="button" @click="showBulkDialog = false">
              Batal
            </Button>
            <Button type="submit" :disabled="bulkForm.processing">
              <Icon name="check-square" class="w-4 h-4 mr-2" />
              {{ bulkForm.processing ? 'Memverifikasi...' : 'Verifikasi Semua' }}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  </AppLayout>
</template>
