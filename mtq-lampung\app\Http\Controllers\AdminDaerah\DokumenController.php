<?php

namespace App\Http\Controllers\AdminDaerah;

use App\Http\Controllers\Controller;
use App\Models\DokumenPeserta;
use App\Models\JenisDokumen;
use App\Models\VerifikasiDokumen;
use App\Models\Pendaftaran;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;
use Inertia\Response;

class DokumenController extends Controller
{
    /**
     * Display a listing of documents for verification
     */
    public function index(Request $request): Response
    {
        $adminWilayah = Auth::user()->id_wilayah;

        $query = DokumenPeserta::with([
            'pendaftaran.peserta.user',
            'pendaftaran.peserta.wilayah',
            'pendaftaran.golongan.cabangLomba',
            'uploadedBy',
            'verifiedBy'
        ]);

        // Filter by region (admin daerah can only see documents from their region)
        $query->whereHas('pendaftaran.peserta', function ($q) use ($adminWilayah) {
            $q->where('id_wilayah', $adminWilayah);
        });

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status_verifikasi', $request->status);
        }

        // Filter by document type
        if ($request->filled('jenis_dokumen')) {
            $query->where('jenis_dokumen', $request->jenis_dokumen);
        }

        // Search by participant name or registration number
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->whereHas('pendaftaran.peserta.user', function($subQ) use ($search) {
                    $subQ->where('nama_lengkap', 'like', "%{$search}%");
                })->orWhereHas('pendaftaran', function($subQ) use ($search) {
                    $subQ->where('nomor_pendaftaran', 'like', "%{$search}%");
                })->orWhere('nama_file', 'like', "%{$search}%");
            });
        }

        $dokumen = $query->orderBy('created_at', 'desc')->paginate(20);

        // Get available document types for filtering
        $jenisOptions = DokumenPeserta::whereHas('pendaftaran.peserta', function ($q) use ($adminWilayah) {
            $q->where('id_wilayah', $adminWilayah);
        })->distinct()->pluck('jenis_dokumen', 'jenis_dokumen')->toArray();

        return Inertia::render('AdminDaerah/Dokumen/Index', [
            'dokumen' => $dokumen,
            'filters' => $request->only(['status', 'jenis_dokumen', 'search']),
            'jenisOptions' => $jenisOptions
        ]);
    }

    /**
     * Show the form for verifying a specific document
     */
    public function show(DokumenPeserta $dokumen): Response
    {
        $adminWilayah = Auth::user()->id_wilayah;

        // Check access for admin daerah
        if ($dokumen->pendaftaran->peserta->id_wilayah !== $adminWilayah) {
            abort(403, 'Akses ditolak');
        }

        $dokumen->load([
            'pendaftaran.peserta.user',
            'pendaftaran.peserta.wilayah',
            'pendaftaran.golongan.cabangLomba',
            'uploadedBy',
            'verifiedBy'
        ]);

        // Get related verification record if exists
        $verifikasi = VerifikasiDokumen::where('id_dokumen', $dokumen->id_dokumen)->first();

        // Get document type information
        $jenisDokumen = JenisDokumen::where('kode_dokumen', $dokumen->jenis_dokumen)->first();

        return Inertia::render('AdminDaerah/Dokumen/Show', [
            'dokumen' => $dokumen,
            'verifikasi' => $verifikasi,
            'jenisDokumen' => $jenisDokumen
        ]);
    }

    /**
     * Verify a document (approve or reject)
     */
    public function verify(Request $request, DokumenPeserta $dokumen)
    {
        $adminWilayah = Auth::user()->id_wilayah;

        // Check access for admin daerah
        if ($dokumen->pendaftaran->peserta->id_wilayah !== $adminWilayah) {
            abort(403, 'Akses ditolak');
        }

        $validated = $request->validate([
            'status_verifikasi' => 'required|in:sesuai,tidak_sesuai,perlu_perbaikan',
            'catatan_verifikasi' => 'nullable|string|max:500',
            'kualitas_gambar' => 'nullable|boolean',
            'kelengkapan_data' => 'nullable|boolean',
            'kejelasan_teks' => 'nullable|boolean'
        ]);

        // Update document status
        $dokumen->update([
            'status_verifikasi' => $validated['status_verifikasi'] === 'sesuai' ? 'approved' : 'rejected',
            'catatan_verifikasi' => $validated['catatan_verifikasi'],
            'verified_by' => Auth::id(),
            'verified_at' => now()
        ]);

        // Create or update verification record
        $jenisDokumen = JenisDokumen::where('kode_dokumen', $dokumen->jenis_dokumen)->first();
        
        if ($jenisDokumen) {
            VerifikasiDokumen::updateOrCreate(
                ['id_dokumen' => $dokumen->id_dokumen],
                [
                    'id_jenis_dokumen' => $jenisDokumen->id_jenis_dokumen,
                    'verified_by' => Auth::id(),
                    'status_verifikasi' => $validated['status_verifikasi'],
                    'catatan_verifikasi' => $validated['catatan_verifikasi'],
                    'verified_at' => now(),
                    'kualitas_gambar' => $validated['kualitas_gambar'] ?? null,
                    'kelengkapan_data' => $validated['kelengkapan_data'] ?? null,
                    'kejelasan_teks' => $validated['kejelasan_teks'] ?? null,
                    'detail_verifikasi' => [
                        'kualitas_gambar' => $validated['kualitas_gambar'] ?? null,
                        'kelengkapan_data' => $validated['kelengkapan_data'] ?? null,
                        'kejelasan_teks' => $validated['kejelasan_teks'] ?? null,
                        'verified_by_name' => Auth::user()->nama_lengkap,
                        'verified_at' => now()->toISOString()
                    ]
                ]
            );
        }

        $message = $validated['status_verifikasi'] === 'sesuai' 
            ? 'Dokumen berhasil disetujui.' 
            : 'Dokumen berhasil ditolak.';

        return back()->with('success', $message);
    }

    /**
     * Download document file
     */
    public function download(DokumenPeserta $dokumen)
    {
        $adminWilayah = Auth::user()->id_wilayah;

        // Check access for admin daerah
        if ($dokumen->pendaftaran->peserta->id_wilayah !== $adminWilayah) {
            abort(403, 'Akses ditolak');
        }

        if (!Storage::disk('public')->exists($dokumen->path_file)) {
            abort(404, 'File tidak ditemukan.');
        }

        return Storage::disk('public')->download($dokumen->path_file, $dokumen->nama_file);
    }

    /**
     * Preview document file
     */
    public function preview(DokumenPeserta $dokumen)
    {
        $adminWilayah = Auth::user()->id_wilayah;

        // Check access for admin daerah
        if ($dokumen->pendaftaran->peserta->id_wilayah !== $adminWilayah) {
            abort(403, 'Akses ditolak');
        }

        if (!Storage::disk('public')->exists($dokumen->path_file)) {
            abort(404, 'File tidak ditemukan.');
        }

        return response()->file(storage_path('app/public/' . $dokumen->path_file));
    }

    /**
     * Bulk verify documents
     */
    public function bulkVerify(Request $request)
    {
        $adminWilayah = Auth::user()->id_wilayah;

        $validated = $request->validate([
            'dokumen_ids' => 'required|array',
            'dokumen_ids.*' => 'exists:dokumen_peserta,id_dokumen',
            'status_verifikasi' => 'required|in:sesuai,tidak_sesuai,perlu_perbaikan',
            'catatan_verifikasi' => 'nullable|string|max:500'
        ]);

        $query = DokumenPeserta::whereIn('id_dokumen', $validated['dokumen_ids']);

        // Check access for admin daerah
        $query->whereHas('pendaftaran.peserta', function ($q) use ($adminWilayah) {
            $q->where('id_wilayah', $adminWilayah);
        });

        $dokumenList = $query->get();

        foreach ($dokumenList as $dokumen) {
            // Update document status
            $dokumen->update([
                'status_verifikasi' => $validated['status_verifikasi'] === 'sesuai' ? 'approved' : 'rejected',
                'catatan_verifikasi' => $validated['catatan_verifikasi'],
                'verified_by' => Auth::id(),
                'verified_at' => now()
            ]);

            // Create or update verification record
            $jenisDokumen = JenisDokumen::where('kode_dokumen', $dokumen->jenis_dokumen)->first();
            
            if ($jenisDokumen) {
                VerifikasiDokumen::updateOrCreate(
                    ['id_dokumen' => $dokumen->id_dokumen],
                    [
                        'id_jenis_dokumen' => $jenisDokumen->id_jenis_dokumen,
                        'verified_by' => Auth::id(),
                        'status_verifikasi' => $validated['status_verifikasi'],
                        'catatan_verifikasi' => $validated['catatan_verifikasi'],
                        'verified_at' => now(),
                        'detail_verifikasi' => [
                            'bulk_verification' => true,
                            'verified_by_name' => Auth::user()->nama_lengkap,
                            'verified_at' => now()->toISOString()
                        ]
                    ]
                );
            }
        }

        $count = $dokumenList->count();
        $message = $validated['status_verifikasi'] === 'sesuai' 
            ? "Berhasil menyetujui {$count} dokumen." 
            : "Berhasil menolak {$count} dokumen.";

        return back()->with('success', $message);
    }

    /**
     * Get document statistics for dashboard
     */
    public function statistics()
    {
        $adminWilayah = Auth::user()->id_wilayah;

        $stats = [
            'total_dokumen' => DokumenPeserta::whereHas('pendaftaran.peserta', function($q) use ($adminWilayah) {
                $q->where('id_wilayah', $adminWilayah);
            })->count(),
            'dokumen_pending' => DokumenPeserta::whereHas('pendaftaran.peserta', function($q) use ($adminWilayah) {
                $q->where('id_wilayah', $adminWilayah);
            })->where('status_verifikasi', 'pending')->count(),
            'dokumen_approved' => DokumenPeserta::whereHas('pendaftaran.peserta', function($q) use ($adminWilayah) {
                $q->where('id_wilayah', $adminWilayah);
            })->where('status_verifikasi', 'approved')->count(),
            'dokumen_rejected' => DokumenPeserta::whereHas('pendaftaran.peserta', function($q) use ($adminWilayah) {
                $q->where('id_wilayah', $adminWilayah);
            })->where('status_verifikasi', 'rejected')->count(),
        ];

        return response()->json($stats);
    }
}
