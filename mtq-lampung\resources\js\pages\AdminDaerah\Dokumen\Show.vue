<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue'
import { Head, useForm } from '@inertiajs/vue3'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import Icon from '@/components/Icon.vue'
import TextLink from '@/components/TextLink.vue'
import { ref } from 'vue'

interface Wilayah {
  nama_wilayah: string
}

interface User {
  nama_lengkap: string
}

interface Peserta {
  nama_lengkap: string
  wilayah: Wilayah
  user: User
}

interface CabangLomba {
  nama_cabang: string
}

interface Golongan {
  nama_golongan: string
  cabang_lomba: CabangLomba
}

interface Pendaftaran {
  nomor_pendaftaran: string
  peserta: Peserta
  golongan: Golongan
}

interface DokumenPeserta {
  id_dokumen: number
  jenis_dokumen: string
  nama_file: string
  path_file: string
  ukuran_file: number
  mime_type: string
  status_verifikasi: string
  catatan_verifikasi: string | null
  verified_at: string | null
  created_at: string
  keterangan: string | null
  pendaftaran: Pendaftaran
  uploaded_by: User
  verified_by: User | null
}

interface VerifikasiDokumen {
  id_verifikasi_dokumen: number
  status_verifikasi: string
  catatan_verifikasi: string | null
  detail_verifikasi: any
  verified_at: string | null
  kualitas_gambar: boolean | null
  kelengkapan_data: boolean | null
  kejelasan_teks: boolean | null
}

interface JenisDokumen {
  id_jenis_dokumen: number
  kode_dokumen: string
  nama_dokumen: string
  deskripsi: string | null
  kategori: string
  wajib: boolean
  format_file: string | null
  ukuran_max_kb: number
}

const props = defineProps<{
  dokumen: DokumenPeserta
  verifikasi?: VerifikasiDokumen
  jenisDokumen?: JenisDokumen
}>()

const verifyForm = useForm({
  status_verifikasi: props.verifikasi?.status_verifikasi || '',
  catatan_verifikasi: props.verifikasi?.catatan_verifikasi || '',
  kualitas_gambar: props.verifikasi?.kualitas_gambar || null as boolean | null,
  kelengkapan_data: props.verifikasi?.kelengkapan_data || null as boolean | null,
  kejelasan_teks: props.verifikasi?.kejelasan_teks || null as boolean | null
})

const showPreviewDialog = ref(false)

function verifyDocument() {
  verifyForm.post(route('admin-daerah.dokumen.verify', props.dokumen.id_dokumen))
}

function openPreview() {
  showPreviewDialog.value = true
}

function getStatusBadgeVariant(status: string) {
  const variants: Record<string, string> = {
    'pending': 'secondary',
    'approved': 'default',
    'rejected': 'destructive'
  }
  return variants[status] || 'secondary'
}

function getStatusLabel(status: string) {
  const labels: Record<string, string> = {
    'pending': 'Menunggu',
    'approved': 'Disetujui',
    'rejected': 'Ditolak'
  }
  return labels[status] || status
}

function formatFileSize(sizeBytes: number): string {
  const sizeKb = sizeBytes / 1024
  if (sizeKb >= 1024) {
    return `${(sizeKb / 1024).toFixed(1)} MB`
  }
  return `${sizeKb.toFixed(1)} KB`
}

function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

function isImageFile(mimeType: string): boolean {
  return mimeType.startsWith('image/')
}

function isPdfFile(mimeType: string): boolean {
  return mimeType === 'application/pdf'
}

const kategoriOptions: Record<string, string> = {
  'identitas': 'Identitas',
  'pendidikan': 'Pendidikan',
  'rekomendasi': 'Rekomendasi',
  'lainnya': 'Lainnya'
}
</script>

<template>
  <AppLayout>
    <Head :title="`Verifikasi Dokumen - ${dokumen.jenis_dokumen}`" />

    <div class="space-y-6">
      <!-- Header -->
      <div class="flex items-center justify-between">
        <div class="islamic-gradient p-6 rounded-lg islamic-shadow">
          <Heading title="Verifikasi Dokumen" class="text-white"/>
          <p class="text-green-100 mt-2">{{ dokumen.jenis_dokumen }} - {{ dokumen.pendaftaran.peserta.nama_lengkap }}</p>
        </div>
        <div class="flex gap-2">
          <Button variant="outline" @click="openPreview" class="islamic-shadow">
            <Icon name="eye" class="w-4 h-4 mr-2" />
            Preview
          </Button>
          <Button variant="outline" as-child class="islamic-shadow">
            <a :href="route('admin-daerah.dokumen.download', dokumen.id_dokumen)" target="_blank">
              <Icon name="download" class="w-4 h-4 mr-2" />
              Download
            </a>
          </Button>
          <Button variant="outline" as-child class="islamic-shadow">
            <TextLink :href="route('admin-daerah.dokumen.index')">
              <Icon name="arrow-left" class="w-4 h-4 mr-2" />
              Kembali
            </TextLink>
          </Button>
        </div>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Document Information -->
        <div class="lg:col-span-2 space-y-6">
          <!-- Document Details -->
          <Card class="islamic-shadow">
            <CardHeader>
              <CardTitle class="text-islamic-700">Informasi Dokumen</CardTitle>
            </CardHeader>
            <CardContent class="space-y-4">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label class="text-sm font-medium text-muted-foreground">Jenis Dokumen</Label>
                  <p class="text-lg mt-1">{{ dokumen.jenis_dokumen }}</p>
                </div>

                <div>
                  <Label class="text-sm font-medium text-muted-foreground">Status</Label>
                  <div class="mt-1">
                    <Badge :variant="getStatusBadgeVariant(dokumen.status_verifikasi)">
                      {{ getStatusLabel(dokumen.status_verifikasi) }}
                    </Badge>
                  </div>
                </div>

                <div>
                  <Label class="text-sm font-medium text-muted-foreground">Nama File</Label>
                  <p class="text-lg mt-1">{{ dokumen.nama_file }}</p>
                </div>

                <div>
                  <Label class="text-sm font-medium text-muted-foreground">Ukuran File</Label>
                  <p class="text-lg mt-1">{{ formatFileSize(dokumen.ukuran_file) }}</p>
                </div>

                <div>
                  <Label class="text-sm font-medium text-muted-foreground">Tipe File</Label>
                  <p class="text-lg mt-1">{{ dokumen.mime_type }}</p>
                </div>

                <div>
                  <Label class="text-sm font-medium text-muted-foreground">Tanggal Upload</Label>
                  <p class="text-lg mt-1">{{ formatDate(dokumen.created_at) }}</p>
                </div>
              </div>

              <div v-if="dokumen.keterangan">
                <Label class="text-sm font-medium text-muted-foreground">Keterangan</Label>
                <p class="mt-1 text-gray-700">{{ dokumen.keterangan }}</p>
              </div>
            </CardContent>
          </Card>

          <!-- Participant Information -->
          <Card class="islamic-shadow">
            <CardHeader>
              <CardTitle class="text-islamic-700">Informasi Peserta</CardTitle>
            </CardHeader>
            <CardContent class="space-y-4">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label class="text-sm font-medium text-muted-foreground">Nama Lengkap</Label>
                  <p class="text-lg mt-1">{{ dokumen.pendaftaran.peserta.nama_lengkap }}</p>
                </div>

                <div>
                  <Label class="text-sm font-medium text-muted-foreground">Nomor Pendaftaran</Label>
                  <p class="text-lg mt-1">{{ dokumen.pendaftaran.nomor_pendaftaran }}</p>
                </div>

                <div>
                  <Label class="text-sm font-medium text-muted-foreground">Wilayah</Label>
                  <p class="text-lg mt-1">{{ dokumen.pendaftaran.peserta.wilayah.nama_wilayah }}</p>
                </div>

                <div>
                  <Label class="text-sm font-medium text-muted-foreground">Cabang Lomba</Label>
                  <p class="text-lg mt-1">{{ dokumen.pendaftaran.golongan.cabang_lomba.nama_cabang }}</p>
                </div>

                <div class="md:col-span-2">
                  <Label class="text-sm font-medium text-muted-foreground">Golongan</Label>
                  <p class="text-lg mt-1">{{ dokumen.pendaftaran.golongan.nama_golongan }}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <!-- Document Type Requirements -->
          <Card v-if="jenisDokumen" class="islamic-shadow">
            <CardHeader>
              <CardTitle class="text-islamic-700">Persyaratan Dokumen</CardTitle>
            </CardHeader>
            <CardContent class="space-y-4">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label class="text-sm font-medium text-muted-foreground">Kategori</Label>
                  <p class="text-lg mt-1">{{ kategoriOptions[jenisDokumen.kategori] }}</p>
                </div>

                <div>
                  <Label class="text-sm font-medium text-muted-foreground">Status Wajib</Label>
                  <div class="mt-1">
                    <Badge :variant="jenisDokumen.wajib ? 'destructive' : 'secondary'">
                      {{ jenisDokumen.wajib ? 'Wajib' : 'Opsional' }}
                    </Badge>
                  </div>
                </div>

                <div>
                  <Label class="text-sm font-medium text-muted-foreground">Format yang Diizinkan</Label>
                  <p class="text-lg mt-1">
                    {{ jenisDokumen.format_file || 'Semua format' }}
                  </p>
                </div>

                <div>
                  <Label class="text-sm font-medium text-muted-foreground">Ukuran Maksimal</Label>
                  <p class="text-lg mt-1">{{ formatFileSize(jenisDokumen.ukuran_max_kb * 1024) }}</p>
                </div>
              </div>

              <div v-if="jenisDokumen.deskripsi">
                <Label class="text-sm font-medium text-muted-foreground">Deskripsi</Label>
                <p class="mt-1 text-gray-700">{{ jenisDokumen.deskripsi }}</p>
              </div>
            </CardContent>
          </Card>
        </div>

        <!-- Verification Panel -->
        <div class="space-y-6">
          <!-- Current Verification Status -->
          <Card v-if="verifikasi" class="islamic-shadow">
            <CardHeader>
              <CardTitle class="text-islamic-700">Status Verifikasi</CardTitle>
            </CardHeader>
            <CardContent class="space-y-4">
              <div>
                <Label class="text-sm font-medium text-muted-foreground">Status</Label>
                <div class="mt-1">
                  <Badge :variant="getStatusBadgeVariant(verifikasi.status_verifikasi)">
                    {{ getStatusLabel(verifikasi.status_verifikasi) }}
                  </Badge>
                </div>
              </div>

              <div v-if="verifikasi.catatan_verifikasi">
                <Label class="text-sm font-medium text-muted-foreground">Catatan</Label>
                <p class="mt-1 text-gray-700">{{ verifikasi.catatan_verifikasi }}</p>
              </div>

              <div v-if="verifikasi.verified_at">
                <Label class="text-sm font-medium text-muted-foreground">Diverifikasi</Label>
                <p class="mt-1 text-sm">{{ formatDate(verifikasi.verified_at) }}</p>
              </div>

              <!-- Quality Checks -->
              <div v-if="verifikasi.kualitas_gambar !== null || verifikasi.kelengkapan_data !== null || verifikasi.kejelasan_teks !== null">
                <Label class="text-sm font-medium text-muted-foreground">Kriteria Verifikasi</Label>
                <div class="mt-2 space-y-2">
                  <div v-if="verifikasi.kualitas_gambar !== null" class="flex items-center gap-2">
                    <Icon :name="verifikasi.kualitas_gambar ? 'check' : 'x'" 
                          :class="verifikasi.kualitas_gambar ? 'text-green-600' : 'text-red-600'" 
                          class="w-4 h-4" />
                    <span class="text-sm">Kualitas gambar</span>
                  </div>
                  <div v-if="verifikasi.kelengkapan_data !== null" class="flex items-center gap-2">
                    <Icon :name="verifikasi.kelengkapan_data ? 'check' : 'x'" 
                          :class="verifikasi.kelengkapan_data ? 'text-green-600' : 'text-red-600'" 
                          class="w-4 h-4" />
                    <span class="text-sm">Kelengkapan data</span>
                  </div>
                  <div v-if="verifikasi.kejelasan_teks !== null" class="flex items-center gap-2">
                    <Icon :name="verifikasi.kejelasan_teks ? 'check' : 'x'" 
                          :class="verifikasi.kejelasan_teks ? 'text-green-600' : 'text-red-600'" 
                          class="w-4 h-4" />
                    <span class="text-sm">Kejelasan teks</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <!-- Verification Form -->
          <Card class="islamic-shadow">
            <CardHeader>
              <CardTitle class="text-islamic-700">
                {{ verifikasi ? 'Update Verifikasi' : 'Verifikasi Dokumen' }}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <form @submit.prevent="verifyDocument" class="space-y-4">
                <div>
                  <Label for="status_verifikasi">Status Verifikasi *</Label>
                  <Select v-model="verifyForm.status_verifikasi" required>
                    <SelectTrigger class="mt-1">
                      <SelectValue placeholder="Pilih status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="sesuai">Sesuai</SelectItem>
                      <SelectItem value="tidak_sesuai">Tidak Sesuai</SelectItem>
                      <SelectItem value="perlu_perbaikan">Perlu Perbaikan</SelectItem>
                    </SelectContent>
                  </Select>
                  <p v-if="verifyForm.errors.status_verifikasi" class="text-sm text-red-600 mt-1">
                    {{ verifyForm.errors.status_verifikasi }}
                  </p>
                </div>

                <div>
                  <Label for="catatan_verifikasi">Catatan Verifikasi</Label>
                  <Textarea
                    id="catatan_verifikasi"
                    v-model="verifyForm.catatan_verifikasi"
                    placeholder="Catatan atau alasan verifikasi..."
                    class="mt-1"
                    rows="3"
                  />
                  <p v-if="verifyForm.errors.catatan_verifikasi" class="text-sm text-red-600 mt-1">
                    {{ verifyForm.errors.catatan_verifikasi }}
                  </p>
                </div>

                <div class="space-y-3">
                  <Label>Kriteria Verifikasi</Label>
                  <div class="space-y-2">
                    <div class="flex items-center space-x-2">
                      <Checkbox v-model:checked="verifyForm.kualitas_gambar" />
                      <Label class="text-sm">Kualitas gambar baik</Label>
                    </div>
                    <div class="flex items-center space-x-2">
                      <Checkbox v-model:checked="verifyForm.kelengkapan_data" />
                      <Label class="text-sm">Data lengkap dan sesuai</Label>
                    </div>
                    <div class="flex items-center space-x-2">
                      <Checkbox v-model:checked="verifyForm.kejelasan_teks" />
                      <Label class="text-sm">Teks jelas dan terbaca</Label>
                    </div>
                  </div>
                </div>

                <Button type="submit" :disabled="verifyForm.processing" class="w-full islamic-gradient">
                  <Icon name="check" class="w-4 h-4 mr-2" />
                  {{ verifyForm.processing ? 'Memverifikasi...' : (verifikasi ? 'Update Verifikasi' : 'Verifikasi Dokumen') }}
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>

    <!-- Preview Dialog -->
    <Dialog v-model:open="showPreviewDialog">
      <DialogContent class="max-w-4xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle>Preview Dokumen</DialogTitle>
          <DialogDescription>
            {{ dokumen.nama_file }}
          </DialogDescription>
        </DialogHeader>
        <div class="flex justify-center items-center min-h-[400px] bg-gray-50 rounded-lg">
          <!-- Image Preview -->
          <img 
            v-if="isImageFile(dokumen.mime_type)"
            :src="route('admin-daerah.dokumen.preview', dokumen.id_dokumen)"
            :alt="dokumen.nama_file"
            class="max-w-full max-h-[60vh] object-contain"
          />
          
          <!-- PDF Preview -->
          <iframe 
            v-else-if="isPdfFile(dokumen.mime_type)"
            :src="route('admin-daerah.dokumen.preview', dokumen.id_dokumen)"
            class="w-full h-[60vh] border-0"
          ></iframe>
          
          <!-- Other file types -->
          <div v-else class="text-center p-8">
            <Icon name="file" class="w-16 h-16 mx-auto text-gray-400 mb-4" />
            <p class="text-gray-600 mb-4">Preview tidak tersedia untuk tipe file ini</p>
            <Button as-child>
              <a :href="route('admin-daerah.dokumen.download', dokumen.id_dokumen)" target="_blank">
                <Icon name="download" class="w-4 h-4 mr-2" />
                Download File
              </a>
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  </AppLayout>
</template>
