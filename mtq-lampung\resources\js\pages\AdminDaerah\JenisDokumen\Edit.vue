<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue'
import { Head, useForm } from '@inertiajs/vue3'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import Icon from '@/components/Icon.vue'
import TextLink from '@/components/TextLink.vue'

interface JenisDokumen {
  id_jenis_dokumen: number
  kode_dokumen: string
  nama_dokumen: string
  deskripsi: string | null
  kategori: string
  wajib: boolean
  format_file: string | null
  ukuran_max_kb: number
  status: string
  urutan: number
}

const props = defineProps<{
  jenisDokumen: JenisDokumen
}>()

const form = useForm({
  kode_dokumen: props.jenisDokumen.kode_dokumen,
  nama_dokumen: props.jenisDokumen.nama_dokumen,
  deskripsi: props.jenisDokumen.deskripsi || '',
  kategori: props.jenisDokumen.kategori,
  wajib: props.jenisDokumen.wajib,
  format_file: props.jenisDokumen.format_file || '',
  ukuran_max_kb: props.jenisDokumen.ukuran_max_kb,
  status: props.jenisDokumen.status,
  urutan: props.jenisDokumen.urutan
})

function submit() {
  form.put(route('admin-daerah.jenis-dokumen.update', props.jenisDokumen.id_jenis_dokumen))
}

const kategoriOptions = {
  'identitas': 'Identitas',
  'pendidikan': 'Pendidikan', 
  'rekomendasi': 'Rekomendasi',
  'lainnya': 'Lainnya'
}

const formatFileOptions = [
  { value: 'pdf', label: 'PDF' },
  { value: 'jpg,jpeg,png', label: 'Gambar (JPG, PNG)' },
  { value: 'pdf,jpg,jpeg,png', label: 'PDF dan Gambar' },
  { value: 'doc,docx,pdf', label: 'Dokumen (DOC, PDF)' }
]
</script>

<template>
  <AppLayout>
    <Head title="Edit Jenis Dokumen" />

    <div class="space-y-6">
      <!-- Header -->
      <div class="flex items-center justify-between">
        <div class="islamic-gradient p-6 rounded-lg islamic-shadow">
          <Heading title="Edit Jenis Dokumen" class="text-white"/>
          <p class="text-green-100 mt-2">Perbarui informasi jenis dokumen</p>
        </div>
        <div class="flex gap-2">
          <Button variant="outline" as-child class="islamic-shadow">
            <TextLink :href="route('admin-daerah.jenis-dokumen.show', jenisDokumen.id_jenis_dokumen)">
              <Icon name="eye" class="w-4 h-4 mr-2" />
              Lihat
            </TextLink>
          </Button>
          <Button variant="outline" as-child class="islamic-shadow">
            <TextLink :href="route('admin-daerah.jenis-dokumen.index')">
              <Icon name="arrow-left" class="w-4 h-4 mr-2" />
              Kembali
            </TextLink>
          </Button>
        </div>
      </div>

      <!-- Form -->
      <Card class="islamic-shadow">
        <CardHeader>
          <CardTitle class="text-islamic-700">Edit Informasi Jenis Dokumen</CardTitle>
          <CardDescription>
            Perbarui informasi untuk jenis dokumen "{{ jenisDokumen.nama_dokumen }}"
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form @submit.prevent="submit" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Kode Dokumen -->
              <div>
                <Label for="kode_dokumen">Kode Dokumen *</Label>
                <Input
                  id="kode_dokumen"
                  v-model="form.kode_dokumen"
                  placeholder="Contoh: KTP, IJAZAH"
                  class="mt-1"
                  :class="{ 'border-red-500': form.errors.kode_dokumen }"
                  required
                />
                <p v-if="form.errors.kode_dokumen" class="text-sm text-red-600 mt-1">
                  {{ form.errors.kode_dokumen }}
                </p>
                <p class="text-sm text-muted-foreground mt-1">
                  Kode unik untuk identifikasi dokumen (maksimal 20 karakter)
                </p>
              </div>

              <!-- Nama Dokumen -->
              <div>
                <Label for="nama_dokumen">Nama Dokumen *</Label>
                <Input
                  id="nama_dokumen"
                  v-model="form.nama_dokumen"
                  placeholder="Contoh: Kartu Tanda Penduduk"
                  class="mt-1"
                  :class="{ 'border-red-500': form.errors.nama_dokumen }"
                  required
                />
                <p v-if="form.errors.nama_dokumen" class="text-sm text-red-600 mt-1">
                  {{ form.errors.nama_dokumen }}
                </p>
              </div>

              <!-- Kategori -->
              <div>
                <Label for="kategori">Kategori *</Label>
                <Select v-model="form.kategori" required>
                  <SelectTrigger class="mt-1" :class="{ 'border-red-500': form.errors.kategori }">
                    <SelectValue placeholder="Pilih kategori" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem 
                      v-for="(label, value) in kategoriOptions" 
                      :key="value" 
                      :value="value"
                    >
                      {{ label }}
                    </SelectItem>
                  </SelectContent>
                </Select>
                <p v-if="form.errors.kategori" class="text-sm text-red-600 mt-1">
                  {{ form.errors.kategori }}
                </p>
              </div>

              <!-- Status -->
              <div>
                <Label for="status">Status *</Label>
                <Select v-model="form.status" required>
                  <SelectTrigger class="mt-1" :class="{ 'border-red-500': form.errors.status }">
                    <SelectValue placeholder="Pilih status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="aktif">Aktif</SelectItem>
                    <SelectItem value="non_aktif">Non Aktif</SelectItem>
                  </SelectContent>
                </Select>
                <p v-if="form.errors.status" class="text-sm text-red-600 mt-1">
                  {{ form.errors.status }}
                </p>
              </div>

              <!-- Format File -->
              <div>
                <Label for="format_file">Format File yang Diizinkan</Label>
                <Select v-model="form.format_file">
                  <SelectTrigger class="mt-1" :class="{ 'border-red-500': form.errors.format_file }">
                    <SelectValue placeholder="Pilih format file atau kosongkan untuk semua format" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Semua Format</SelectItem>
                    <SelectItem 
                      v-for="option in formatFileOptions" 
                      :key="option.value" 
                      :value="option.value"
                    >
                      {{ option.label }}
                    </SelectItem>
                  </SelectContent>
                </Select>
                <p v-if="form.errors.format_file" class="text-sm text-red-600 mt-1">
                  {{ form.errors.format_file }}
                </p>
                <p class="text-sm text-muted-foreground mt-1">
                  Kosongkan untuk mengizinkan semua format file
                </p>
              </div>

              <!-- Ukuran Maksimal -->
              <div>
                <Label for="ukuran_max_kb">Ukuran Maksimal (KB) *</Label>
                <Input
                  id="ukuran_max_kb"
                  v-model.number="form.ukuran_max_kb"
                  type="number"
                  min="100"
                  max="10240"
                  class="mt-1"
                  :class="{ 'border-red-500': form.errors.ukuran_max_kb }"
                  required
                />
                <p v-if="form.errors.ukuran_max_kb" class="text-sm text-red-600 mt-1">
                  {{ form.errors.ukuran_max_kb }}
                </p>
                <p class="text-sm text-muted-foreground mt-1">
                  Ukuran file maksimal dalam KB (100 - 10240 KB)
                </p>
              </div>

              <!-- Urutan -->
              <div>
                <Label for="urutan">Urutan Tampil *</Label>
                <Input
                  id="urutan"
                  v-model.number="form.urutan"
                  type="number"
                  min="0"
                  class="mt-1"
                  :class="{ 'border-red-500': form.errors.urutan }"
                  required
                />
                <p v-if="form.errors.urutan" class="text-sm text-red-600 mt-1">
                  {{ form.errors.urutan }}
                </p>
                <p class="text-sm text-muted-foreground mt-1">
                  Urutan tampil dokumen (semakin kecil semakin atas)
                </p>
              </div>
            </div>

            <!-- Deskripsi -->
            <div>
              <Label for="deskripsi">Deskripsi</Label>
              <Textarea
                id="deskripsi"
                v-model="form.deskripsi"
                placeholder="Deskripsi atau keterangan tambahan untuk jenis dokumen ini..."
                class="mt-1"
                :class="{ 'border-red-500': form.errors.deskripsi }"
                rows="3"
              />
              <p v-if="form.errors.deskripsi" class="text-sm text-red-600 mt-1">
                {{ form.errors.deskripsi }}
              </p>
            </div>

            <!-- Wajib -->
            <div class="flex items-center space-x-2">
              <Checkbox 
                id="wajib" 
                v-model:checked="form.wajib"
                :class="{ 'border-red-500': form.errors.wajib }"
              />
              <Label for="wajib" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                Dokumen ini wajib diunggah
              </Label>
            </div>
            <p v-if="form.errors.wajib" class="text-sm text-red-600">
              {{ form.errors.wajib }}
            </p>

            <!-- Submit Button -->
            <div class="flex justify-end gap-4 pt-6 border-t">
              <Button variant="outline" type="button" as-child>
                <TextLink :href="route('admin-daerah.jenis-dokumen.index')">
                  Batal
                </TextLink>
              </Button>
              <Button type="submit" :disabled="form.processing" class="islamic-gradient">
                <Icon name="save" class="w-4 h-4 mr-2" />
                {{ form.processing ? 'Menyimpan...' : 'Perbarui' }}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  </AppLayout>
</template>
