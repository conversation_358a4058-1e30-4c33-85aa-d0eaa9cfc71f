<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Peserta;
use App\Models\Pendaftaran;
use App\Models\Golongan;

class CreateTestPendaftaran extends Command
{
    protected $signature = 'test:create-pendaftaran';
    protected $description = 'Create test pendaftaran for testing upload';

    public function handle()
    {
        $this->info('Creating test pendaftaran...');

        // Get first peserta
        $peserta = Peserta::first();
        if (!$peserta) {
            $this->error('No peserta found');
            return;
        }

        $this->info('Using peserta: ' . $peserta->nama_lengkap);

        // Check if peserta already has pendaftaran
        if ($peserta->pendaftaran->isNotEmpty()) {
            $this->info('Peserta already has pendaftaran');
            return;
        }

        // Get first golongan
        $golongan = Golongan::first();
        if (!$golongan) {
            $this->error('No golongan found');
            return;
        }

        $this->info('Using golongan: ' . $golongan->nama_golongan);

        // Create pendaftaran
        $pendaftaran = Pendaftaran::create([
            'id_peserta' => $peserta->id_peserta,
            'id_golongan' => $golongan->id_golongan,
            'nomor_pendaftaran' => 'TEST-' . time(),
            'nomor_peserta' => 'P-' . str_pad($peserta->id_peserta, 4, '0', STR_PAD_LEFT),
            'nomor_urut' => 1,
            'tahun_pendaftaran' => date('Y'),
            'status_pendaftaran' => 'approved',
            'tanggal_daftar' => now(),
            'registered_by' => 1, // Assuming admin user ID 1
            'keterangan' => 'Test pendaftaran for upload testing'
        ]);

        $this->info('Pendaftaran created successfully!');
        $this->info('Pendaftaran ID: ' . $pendaftaran->id_pendaftaran);
        $this->info('Nomor Pendaftaran: ' . $pendaftaran->nomor_pendaftaran);
    }
}
