<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue'
import { Head, Link, router, useForm } from '@inertiajs/vue3'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import Icon from '@/components/Icon.vue'
import TextLink from '@/components/TextLink.vue'
import { ref } from 'vue'

interface JenisDokumen {
  id_jenis_dokumen: number
  kode_dokumen: string
  nama_dokumen: string
  deskripsi: string | null
  kategori: string
  wajib: boolean
  format_file: string | null
  ukuran_max_kb: number
  status: string
  urutan: number
  created_at: string
  updated_at: string
}

interface PaginatedData {
  data: JenisDokumen[]
  current_page: number
  last_page: number
  per_page: number
  total: number
  links: Array<{
    url: string | null
    label: string
    active: boolean
  }>
}

const props = defineProps<{
  jenisDokumen: PaginatedData
  filters: {
    status?: string
    kategori?: string
    search?: string
  }
  kategoriOptions: Record<string, string>
}>()

const searchForm = useForm({
  search: props.filters.search || '',
  status: props.filters.status || '',
  kategori: props.filters.kategori || ''
})

const deleteForm = useForm({})
const toggleForm = useForm({})

const showDeleteDialog = ref(false)
const selectedItem = ref<JenisDokumen | null>(null)

function search() {
  searchForm.get(route('admin-daerah.jenis-dokumen.index'), {
    preserveState: true,
    replace: true
  })
}

function clearFilters() {
  searchForm.reset()
  search()
}

function confirmDelete(item: JenisDokumen) {
  selectedItem.value = item
  showDeleteDialog.value = true
}

function deleteItem() {
  if (!selectedItem.value) return
  
  deleteForm.delete(route('admin-daerah.jenis-dokumen.destroy', selectedItem.value.id_jenis_dokumen), {
    onSuccess: () => {
      showDeleteDialog.value = false
      selectedItem.value = null
    }
  })
}

function toggleStatus(item: JenisDokumen) {
  toggleForm.post(route('admin-daerah.jenis-dokumen.toggle-status', item.id_jenis_dokumen))
}

function getStatusBadgeVariant(status: string) {
  return status === 'aktif' ? 'default' : 'secondary'
}

function getKategoriBadgeVariant(kategori: string) {
  const variants: Record<string, string> = {
    'identitas': 'default',
    'pendidikan': 'secondary',
    'rekomendasi': 'outline',
    'lainnya': 'destructive'
  }
  return variants[kategori] || 'default'
}

function formatFileSize(sizeKb: number): string {
  if (sizeKb >= 1024) {
    return `${(sizeKb / 1024).toFixed(1)} MB`
  }
  return `${sizeKb} KB`
}
</script>

<template>
  <AppLayout>
    <Head title="Manajemen Jenis Dokumen" />

    <div class="space-y-6">
      <!-- Header -->
      <div class="flex items-center justify-between">
        <div class="islamic-gradient p-6 rounded-lg islamic-shadow">
          <Heading title="Manajemen Jenis Dokumen" class="text-white"/>
          <p class="text-green-100 mt-2">Kelola jenis dokumen yang diperlukan untuk pendaftaran MTQ</p>
        </div>
        <Button as-child class="islamic-shadow">
          <TextLink :href="route('admin-daerah.jenis-dokumen.create')">
            <Icon name="plus" class="w-4 h-4 mr-2" />
            Tambah Jenis Dokumen
          </TextLink>
        </Button>
      </div>

      <!-- Filters -->
      <Card class="islamic-shadow">
        <CardHeader>
          <CardTitle class="text-islamic-700">Filter & Pencarian</CardTitle>
        </CardHeader>
        <CardContent>
          <form @submit.prevent="search" class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label for="search">Pencarian</Label>
              <Input
                id="search"
                v-model="searchForm.search"
                placeholder="Cari nama atau kode dokumen..."
                class="mt-1"
              />
            </div>
            <div>
              <Label for="status">Status</Label>
              <Select v-model="searchForm.status">
                <SelectTrigger class="mt-1">
                  <SelectValue placeholder="Semua Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Semua Status</SelectItem>
                  <SelectItem value="aktif">Aktif</SelectItem>
                  <SelectItem value="non_aktif">Non Aktif</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label for="kategori">Kategori</Label>
              <Select v-model="searchForm.kategori">
                <SelectTrigger class="mt-1">
                  <SelectValue placeholder="Semua Kategori" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Semua Kategori</SelectItem>
                  <SelectItem 
                    v-for="(label, value) in kategoriOptions" 
                    :key="value" 
                    :value="value"
                  >
                    {{ label }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div class="flex items-end gap-2">
              <Button type="submit" :disabled="searchForm.processing">
                <Icon name="search" class="w-4 h-4 mr-2" />
                Cari
              </Button>
              <Button type="button" variant="outline" @click="clearFilters">
                <Icon name="x" class="w-4 h-4 mr-2" />
                Reset
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      <!-- Data Table -->
      <Card class="islamic-shadow">
        <CardHeader>
          <CardTitle class="text-islamic-700">Daftar Jenis Dokumen</CardTitle>
          <CardDescription>
            Total: {{ jenisDokumen.total }} jenis dokumen
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div class="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Kode</TableHead>
                  <TableHead>Nama Dokumen</TableHead>
                  <TableHead>Kategori</TableHead>
                  <TableHead>Wajib</TableHead>
                  <TableHead>Format</TableHead>
                  <TableHead>Ukuran Max</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Urutan</TableHead>
                  <TableHead class="text-right">Aksi</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow v-for="item in jenisDokumen.data" :key="item.id_jenis_dokumen">
                  <TableCell class="font-mono">{{ item.kode_dokumen }}</TableCell>
                  <TableCell>
                    <div>
                      <div class="font-medium">{{ item.nama_dokumen }}</div>
                      <div v-if="item.deskripsi" class="text-sm text-muted-foreground">
                        {{ item.deskripsi }}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge :variant="getKategoriBadgeVariant(item.kategori)">
                      {{ kategoriOptions[item.kategori] }}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge :variant="item.wajib ? 'destructive' : 'secondary'">
                      {{ item.wajib ? 'Wajib' : 'Opsional' }}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <span v-if="item.format_file" class="text-sm font-mono">
                      {{ item.format_file }}
                    </span>
                    <span v-else class="text-muted-foreground">Semua format</span>
                  </TableCell>
                  <TableCell>{{ formatFileSize(item.ukuran_max_kb) }}</TableCell>
                  <TableCell>
                    <Badge :variant="getStatusBadgeVariant(item.status)">
                      {{ item.status === 'aktif' ? 'Aktif' : 'Non Aktif' }}
                    </Badge>
                  </TableCell>
                  <TableCell>{{ item.urutan }}</TableCell>
                  <TableCell class="text-right">
                    <div class="flex items-center justify-end gap-2">
                      <Button variant="outline" size="sm" as-child>
                        <TextLink :href="route('admin-daerah.jenis-dokumen.show', item.id_jenis_dokumen)">
                          <Icon name="eye" class="w-4 h-4" />
                        </TextLink>
                      </Button>
                      <Button variant="outline" size="sm" as-child>
                        <TextLink :href="route('admin-daerah.jenis-dokumen.edit', item.id_jenis_dokumen)">
                          <Icon name="edit" class="w-4 h-4" />
                        </TextLink>
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        @click="toggleStatus(item)"
                        :disabled="toggleForm.processing"
                      >
                        <Icon :name="item.status === 'aktif' ? 'toggle-right' : 'toggle-left'" class="w-4 h-4" />
                      </Button>
                      <Button 
                        variant="destructive" 
                        size="sm" 
                        @click="confirmDelete(item)"
                      >
                        <Icon name="trash" class="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>

          <!-- Pagination -->
          <div v-if="jenisDokumen.last_page > 1" class="mt-4 flex justify-center">
            <div class="flex items-center gap-2">
              <Button
                v-for="link in jenisDokumen.links"
                :key="link.label"
                :variant="link.active ? 'default' : 'outline'"
                size="sm"
                :disabled="!link.url"
                @click="link.url && router.visit(link.url)"
                v-html="link.label"
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- Delete Confirmation Dialog -->
    <Dialog v-model:open="showDeleteDialog">
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Konfirmasi Hapus</DialogTitle>
          <DialogDescription>
            Apakah Anda yakin ingin menghapus jenis dokumen "{{ selectedItem?.nama_dokumen }}"?
            Tindakan ini tidak dapat dibatalkan.
          </DialogDescription>
        </DialogHeader>
        <div class="flex justify-end gap-2 mt-4">
          <Button variant="outline" @click="showDeleteDialog = false">
            Batal
          </Button>
          <Button 
            variant="destructive" 
            @click="deleteItem"
            :disabled="deleteForm.processing"
          >
            <Icon name="trash" class="w-4 h-4 mr-2" />
            Hapus
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  </AppLayout>
</template>
