<?php

namespace App\Http\Controllers\AdminProvinsi;

use App\Http\Controllers\Controller;
use App\Models\Pendaftaran;
use App\Models\VerifikasiPendaftaran;
use App\Models\DokumenPeserta;
use App\Models\JenisDokumen;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Inertia\Response;

class VerifikasiController extends Controller
{
    /**
     * Display a listing of registrations for verification
     */
    public function index(Request $request): Response
    {
        $query = Pendaftaran::with([
            'peserta.user',
            'peserta.wilayah',
            'golongan.cabangLomba',
            'verifikasiPendaftaran',
            'dokumenPeserta'
        ])
        // ->whereHas('peserta', function($q) {
        //     $q->where('status_peserta', 'verified');
        // })
        ->where('status_pendaftaran', 'verified');

        // Filter by verification status
        if ($request->filled('status_verifikasi')) {
            if ($request->status_verifikasi === 'pending') {
                $query->whereDoesntHave('verifikasiPendaftaran')
                    ->orWhereHas('verifikasiPendaftaran', function($q) {
                        $q->where('status_verifikasi', 'pending');
                    });
            } else {
                $query->whereHas('verifikasiPendaftaran', function($q) use ($request) {
                    $q->where('status_verifikasi', $request->status_verifikasi);
                });
            }
        }

        // Filter by region
        if ($request->filled('wilayah')) {
            $query->whereHas('peserta', function($q) use ($request) {
                $q->where('id_wilayah', $request->wilayah);
            });
        }

        // Filter by category
        if ($request->filled('golongan')) {
            $query->where('id_golongan', $request->golongan);
        }

        // Search by name or registration number
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('nomor_pendaftaran', 'like', "%{$search}%")
                  ->orWhere('nomor_peserta', 'like', "%{$search}%")
                  ->orWhereHas('peserta', function($subQ) use ($search) {
                      $subQ->where('nama_lengkap', 'like', "%{$search}%")
                           ->orWhere('nik', 'like', "%{$search}%");
                  });
            });
        }

        $pendaftaran = $query->orderBy('created_at', 'desc')->paginate(20);

        // Get filter options
        $wilayah = \App\Models\Wilayah::where('level_wilayah', 'kabupaten')
            ->where('status', 'aktif')
            ->orderBy('nama_wilayah')
            ->get();

        $golongan = \App\Models\Golongan::with('cabangLomba')
            ->where('status', 'aktif')
            ->orderBy('nama_golongan')
            ->get();

        return Inertia::render('AdminProvinsi/Verifikasi/Index', [
            'pendaftaran' => $pendaftaran,
            'filters' => $request->only(['status_verifikasi', 'wilayah', 'golongan', 'search']),
            'wilayah' => $wilayah,
            'golongan' => $golongan
        ]);
    }

    /**
     * Show verification form for a specific registration
     */
    public function show(Pendaftaran $pendaftaran): Response
    {
        // Check if registration is eligible for verification
        if ( $pendaftaran->status_pendaftaran !== 'verified') {
            abort(403, 'Pendaftaran tidak dapat diverifikasi');
        }

        $pendaftaran->load([
            'peserta.user',
            'peserta.wilayah',
            'golongan.cabangLomba',
            'verifikasiPendaftaran',
            'dokumenPeserta.verifikasiDokumen'
        ]);

        // Get required document types
        $jenisDokumen = JenisDokumen::aktif()->wajib()->ordered()->get();

        return Inertia::render('AdminProvinsi/Verifikasi/Show', [
            'pendaftaran' => $pendaftaran,
            'jenisDokumen' => $jenisDokumen
        ]);
    }

    /**
     * Perform verification for a registration
     */
    public function verify(Request $request, Pendaftaran $pendaftaran)
    {
        // Check if registration is eligible for verification
        if ($pendaftaran->peserta->status_peserta !== 'verified' ||
            $pendaftaran->status_pendaftaran !== 'verified') {
            abort(403, 'Pendaftaran tidak dapat diverifikasi');
        }

        $validated = $request->validate([
            'status_nik' => 'required|in:sesuai,tidak_sesuai',
            'catatan_nik' => 'nullable|string|max:500',
            'status_dokumen' => 'required|in:berkas_sesuai,berkas_tidak_sesuai',
            'catatan_dokumen' => 'nullable|string|max:500',
            'catatan_verifikasi' => 'nullable|string|max:1000'
        ]);

        DB::transaction(function () use ($validated, $pendaftaran) {
            // Create or update verification record
            $verifikasi = VerifikasiPendaftaran::updateOrCreate(
                ['id_pendaftaran' => $pendaftaran->id_pendaftaran],
                [
                    'verified_by' => Auth::id(),
                    'status_nik' => $validated['status_nik'],
                    'catatan_nik' => $validated['catatan_nik'],
                    'verified_nik_at' => now(),
                    'status_dokumen' => $validated['status_dokumen'],
                    'catatan_dokumen' => $validated['catatan_dokumen'],
                    'verified_dokumen_at' => now(),
                    'catatan_verifikasi' => $validated['catatan_verifikasi'],
                    'verified_at' => now()
                ]
            );

            // Determine overall verification status
            if ($validated['status_nik'] === 'sesuai' && $validated['status_dokumen'] === 'berkas_sesuai') {
                $verifikasi->status_verifikasi = 'approved';

                // Update pendaftaran status to approved
                $pendaftaran->update([
                    'status_pendaftaran' => 'approved',
                    'approved_by' => Auth::id(),
                    'approved_at' => now()
                ]);

                // Update peserta status to approved
                $pendaftaran->peserta->update([
                    'status_peserta' => 'approved'
                ]);
            } else {
                $verifikasi->status_verifikasi = 'rejected';

                // Update pendaftaran status to rejected
                $pendaftaran->update([
                    'status_pendaftaran' => 'rejected',
                    'catatan_approval' => $validated['catatan_verifikasi']
                ]);

                // Update peserta status to rejected
                $pendaftaran->peserta->update([
                    'status_peserta' => 'rejected'
                ]);
            }

            $verifikasi->save();
        });

        $message = $validated['status_nik'] === 'sesuai' && $validated['status_dokumen'] === 'berkas_sesuai'
            ? 'Pendaftaran berhasil disetujui.'
            : 'Pendaftaran berhasil ditolak.';

        return back()->with('success', $message);
    }

    /**
     * Get document preview
     */
    public function previewDocument(DokumenPeserta $dokumen)
    {
        // Check if document belongs to a verified registration
        if ($dokumen->pendaftaran->peserta->status_peserta !== 'verified' ||
            $dokumen->pendaftaran->status_pendaftaran !== 'verified') {
            abort(403, 'Dokumen tidak dapat diakses');
        }

        if (!file_exists(storage_path('app/public/' . $dokumen->path_file))) {
            abort(404, 'File tidak ditemukan.');
        }

        return response()->file(storage_path('app/public/' . $dokumen->path_file));
    }

    /**
     * Download document
     */
    public function downloadDocument(DokumenPeserta $dokumen)
    {
        // Check if document belongs to a verified registration
        if ($dokumen->pendaftaran->peserta->status_peserta !== 'verified' ||
            $dokumen->pendaftaran->status_pendaftaran !== 'verified') {
            abort(403, 'Dokumen tidak dapat diakses');
        }

        if (!file_exists(storage_path('app/public/' . $dokumen->path_file))) {
            abort(404, 'File tidak ditemukan.');
        }

        return response()->download(
            storage_path('app/public/' . $dokumen->path_file),
            $dokumen->nama_file
        );
    }
}
