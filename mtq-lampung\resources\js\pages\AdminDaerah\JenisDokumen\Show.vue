<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue'
import { Head, useForm } from '@inertiajs/vue3'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Label } from '@/components/ui/label'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import Icon from '@/components/Icon.vue'
import TextLink from '@/components/TextLink.vue'
import { ref } from 'vue'

interface VerifikasiDokumen {
  id_verifikasi_dokumen: number
  status_verifikasi: string
  verified_at: string | null
}

interface JenisDokumen {
  id_jenis_dokumen: number
  kode_dokumen: string
  nama_dokumen: string
  deskripsi: string | null
  kategori: string
  wajib: boolean
  format_file: string | null
  ukuran_max_kb: number
  status: string
  urutan: number
  created_at: string
  updated_at: string
  verifikasi_dokumen?: VerifikasiDokumen[]
}

const props = defineProps<{
  jenisDokumen: JenisDokumen
}>()

const deleteForm = useForm({})
const toggleForm = useForm({})

const showDeleteDialog = ref(false)

function confirmDelete() {
  showDeleteDialog.value = true
}

function deleteItem() {
  deleteForm.delete(route('admin-daerah.jenis-dokumen.destroy', props.jenisDokumen.id_jenis_dokumen), {
    onSuccess: () => {
      // Will redirect to index page
    }
  })
}

function toggleStatus() {
  toggleForm.post(route('admin-daerah.jenis-dokumen.toggle-status', props.jenisDokumen.id_jenis_dokumen))
}

function getStatusBadgeVariant(status: string) {
  return status === 'aktif' ? 'default' : 'secondary'
}

function getKategoriBadgeVariant(kategori: string) {
  const variants: Record<string, string> = {
    'identitas': 'default',
    'pendidikan': 'secondary',
    'rekomendasi': 'outline',
    'lainnya': 'destructive'
  }
  return variants[kategori] || 'default'
}

function formatFileSize(sizeKb: number): string {
  if (sizeKb >= 1024) {
    return `${(sizeKb / 1024).toFixed(1)} MB`
  }
  return `${sizeKb} KB`
}

function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const kategoriOptions: Record<string, string> = {
  'identitas': 'Identitas',
  'pendidikan': 'Pendidikan',
  'rekomendasi': 'Rekomendasi',
  'lainnya': 'Lainnya'
}
</script>

<template>
  <AppLayout>
    <Head :title="`Detail Jenis Dokumen - ${jenisDokumen.nama_dokumen}`" />

    <div class="space-y-6">
      <!-- Header -->
      <div class="flex items-center justify-between">
        <div class="islamic-gradient p-6 rounded-lg islamic-shadow">
          <Heading :title="`Detail Jenis Dokumen`" class="text-white"/>
          <p class="text-green-100 mt-2">{{ jenisDokumen.nama_dokumen }}</p>
        </div>
        <div class="flex gap-2">
          <Button variant="outline" as-child class="islamic-shadow">
            <TextLink :href="route('admin-daerah.jenis-dokumen.edit', jenisDokumen.id_jenis_dokumen)">
              <Icon name="edit" class="w-4 h-4 mr-2" />
              Edit
            </TextLink>
          </Button>
          <Button variant="outline" as-child class="islamic-shadow">
            <TextLink :href="route('admin-daerah.jenis-dokumen.index')">
              <Icon name="arrow-left" class="w-4 h-4 mr-2" />
              Kembali
            </TextLink>
          </Button>
        </div>
      </div>

      <!-- Document Type Details -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Information -->
        <div class="lg:col-span-2">
          <Card class="islamic-shadow">
            <CardHeader>
              <CardTitle class="text-islamic-700">Informasi Jenis Dokumen</CardTitle>
              <CardDescription>
                Detail lengkap jenis dokumen
              </CardDescription>
            </CardHeader>
            <CardContent class="space-y-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label class="text-sm font-medium text-muted-foreground">Kode Dokumen</Label>
                  <p class="text-lg font-mono mt-1">{{ jenisDokumen.kode_dokumen }}</p>
                </div>

                <div>
                  <Label class="text-sm font-medium text-muted-foreground">Nama Dokumen</Label>
                  <p class="text-lg mt-1">{{ jenisDokumen.nama_dokumen }}</p>
                </div>

                <div>
                  <Label class="text-sm font-medium text-muted-foreground">Kategori</Label>
                  <div class="mt-1">
                    <Badge :variant="getKategoriBadgeVariant(jenisDokumen.kategori)">
                      {{ kategoriOptions[jenisDokumen.kategori] }}
                    </Badge>
                  </div>
                </div>

                <div>
                  <Label class="text-sm font-medium text-muted-foreground">Status</Label>
                  <div class="mt-1">
                    <Badge :variant="getStatusBadgeVariant(jenisDokumen.status)">
                      {{ jenisDokumen.status === 'aktif' ? 'Aktif' : 'Non Aktif' }}
                    </Badge>
                  </div>
                </div>

                <div>
                  <Label class="text-sm font-medium text-muted-foreground">Wajib</Label>
                  <div class="mt-1">
                    <Badge :variant="jenisDokumen.wajib ? 'destructive' : 'secondary'">
                      {{ jenisDokumen.wajib ? 'Wajib' : 'Opsional' }}
                    </Badge>
                  </div>
                </div>

                <div>
                  <Label class="text-sm font-medium text-muted-foreground">Urutan Tampil</Label>
                  <p class="text-lg mt-1">{{ jenisDokumen.urutan }}</p>
                </div>

                <div>
                  <Label class="text-sm font-medium text-muted-foreground">Format File</Label>
                  <p class="text-lg mt-1">
                    <span v-if="jenisDokumen.format_file" class="font-mono">
                      {{ jenisDokumen.format_file }}
                    </span>
                    <span v-else class="text-muted-foreground">Semua format</span>
                  </p>
                </div>

                <div>
                  <Label class="text-sm font-medium text-muted-foreground">Ukuran Maksimal</Label>
                  <p class="text-lg mt-1">{{ formatFileSize(jenisDokumen.ukuran_max_kb) }}</p>
                </div>
              </div>

              <div v-if="jenisDokumen.deskripsi">
                <Label class="text-sm font-medium text-muted-foreground">Deskripsi</Label>
                <p class="mt-1 text-gray-700 leading-relaxed">{{ jenisDokumen.deskripsi }}</p>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-6 pt-4 border-t">
                <div>
                  <Label class="text-sm font-medium text-muted-foreground">Dibuat</Label>
                  <p class="text-sm mt-1">{{ formatDate(jenisDokumen.created_at) }}</p>
                </div>

                <div>
                  <Label class="text-sm font-medium text-muted-foreground">Terakhir Diperbarui</Label>
                  <p class="text-sm mt-1">{{ formatDate(jenisDokumen.updated_at) }}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <!-- Actions & Statistics -->
        <div class="space-y-6">
          <!-- Quick Actions -->
          <Card class="islamic-shadow">
            <CardHeader>
              <CardTitle class="text-islamic-700">Aksi Cepat</CardTitle>
            </CardHeader>
            <CardContent class="space-y-3">
              <Button 
                variant="outline" 
                class="w-full justify-start"
                as-child
              >
                <TextLink :href="route('admin-daerah.jenis-dokumen.edit', jenisDokumen.id_jenis_dokumen)">
                  <Icon name="edit" class="w-4 h-4 mr-2" />
                  Edit Jenis Dokumen
                </TextLink>
              </Button>

              <Button 
                variant="outline" 
                class="w-full justify-start"
                @click="toggleStatus"
                :disabled="toggleForm.processing"
              >
                <Icon :name="jenisDokumen.status === 'aktif' ? 'toggle-right' : 'toggle-left'" class="w-4 h-4 mr-2" />
                {{ jenisDokumen.status === 'aktif' ? 'Nonaktifkan' : 'Aktifkan' }}
              </Button>

              <Button 
                variant="destructive" 
                class="w-full justify-start"
                @click="confirmDelete"
              >
                <Icon name="trash" class="w-4 h-4 mr-2" />
                Hapus Jenis Dokumen
              </Button>
            </CardContent>
          </Card>

          <!-- Usage Statistics -->
          <Card class="islamic-shadow">
            <CardHeader>
              <CardTitle class="text-islamic-700">Statistik Penggunaan</CardTitle>
            </CardHeader>
            <CardContent>
              <div class="space-y-4">
                <div class="flex justify-between items-center">
                  <span class="text-sm text-muted-foreground">Total Verifikasi</span>
                  <span class="font-medium">
                    {{ jenisDokumen.verifikasi_dokumen?.length || 0 }}
                  </span>
                </div>
                
                <div class="flex justify-between items-center">
                  <span class="text-sm text-muted-foreground">Status</span>
                  <Badge :variant="getStatusBadgeVariant(jenisDokumen.status)">
                    {{ jenisDokumen.status === 'aktif' ? 'Aktif' : 'Non Aktif' }}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Dialog -->
    <Dialog v-model:open="showDeleteDialog">
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Konfirmasi Hapus</DialogTitle>
          <DialogDescription>
            Apakah Anda yakin ingin menghapus jenis dokumen "{{ jenisDokumen.nama_dokumen }}"?
            <br><br>
            <strong>Peringatan:</strong> Tindakan ini tidak dapat dibatalkan dan akan menghapus semua data terkait.
          </DialogDescription>
        </DialogHeader>
        <div class="flex justify-end gap-2 mt-4">
          <Button variant="outline" @click="showDeleteDialog = false">
            Batal
          </Button>
          <Button 
            variant="destructive" 
            @click="deleteItem"
            :disabled="deleteForm.processing"
          >
            <Icon name="trash" class="w-4 h-4 mr-2" />
            {{ deleteForm.processing ? 'Menghapus...' : 'Hapus' }}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  </AppLayout>
</template>
