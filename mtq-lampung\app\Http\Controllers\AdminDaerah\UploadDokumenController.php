<?php

namespace App\Http\Controllers\AdminDaerah;

use App\Http\Controllers\Controller;
use App\Models\DokumenPeserta;
use App\Models\JenisDokumen;
use App\Models\Pendaftaran;
use App\Models\Peserta;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Inertia\Response;

class UploadDokumenController extends Controller
{
    /**
     * Display a listing of participants for document upload
     */
    public function index(Request $request): Response
    {
        $adminWilayah = Auth::user()->id_wilayah;

        $query = Peserta::with([
            'user',
            'wilayah',
            'pendaftaran.golongan.cabangLomba',
            'pendaftaran.dokumenPeserta'
        ])->where('id_wilayah', $adminWilayah);

        // Search by participant name or registration number
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('nama_lengkap', 'like', "%{$search}%")
                  ->orWhereHas('user', function($subQ) use ($search) {
                      $subQ->where('nama_lengkap', 'like', "%{$search}%");
                  })
                  ->orWhereHas('pendaftaran', function($subQ) use ($search) {
                      $subQ->where('nomor_pendaftaran', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status_peserta', $request->status);
        }

        $peserta = $query->orderBy('created_at', 'desc')->paginate(20);

        return Inertia::render('AdminDaerah/UploadDokumen/Index', [
            'peserta' => $peserta,
            'filters' => $request->only(['search', 'status'])
        ]);
    }

    /**
     * Show upload form for specific participant
     */
    public function show(Peserta $peserta): Response
    {
        $adminWilayah = Auth::user()->id_wilayah;

        // Check access for admin daerah
        if ($peserta->id_wilayah !== $adminWilayah) {
            abort(403, 'Akses ditolak');
        }

        $peserta->load([
            'user',
            'wilayah',
            'pendaftaran.golongan.cabangLomba',
            'pendaftaran.dokumenPeserta'
        ]);

        // Get active document types
        $jenisDokumen = JenisDokumen::aktif()->ordered()->get();

        // Get existing documents for this participant
        $existingDocuments = [];
        if ($peserta->pendaftaran->isNotEmpty()) {
            $pendaftaran = $peserta->pendaftaran->first();
            $existingDocuments = DokumenPeserta::where('id_pendaftaran', $pendaftaran->id_pendaftaran)
                ->get()
                ->keyBy('jenis_dokumen');
        }

        return Inertia::render('AdminDaerah/UploadDokumen/Show', [
            'peserta' => $peserta,
            'jenisDokumen' => $jenisDokumen,
            'existingDocuments' => $existingDocuments
        ]);
    }

    /**
     * Upload document for participant
     */
    public function upload(Request $request, Peserta $peserta)
    {
        try {
            $adminWilayah = Auth::user()->id_wilayah;

            // Check access for admin daerah
            if ($peserta->id_wilayah !== $adminWilayah) {
                abort(403, 'Akses ditolak');
            }

            Log::info('Upload request received', [
                'peserta_id' => $peserta->id_peserta,
                'admin_wilayah' => $adminWilayah,
                'request_data' => $request->all(),
                'has_file' => $request->hasFile('file')
            ]);
        } catch (\Exception $e) {
            Log::error('Upload error at start: ' . $e->getMessage());
            return back()->with('error', 'Terjadi kesalahan: ' . $e->getMessage());
        }

        // Get participant's registration
        $pendaftaran = $peserta->pendaftaran->first();
        if (!$pendaftaran) {
            Log::error('Upload failed: No pendaftaran found', ['peserta_id' => $peserta->id_peserta]);
            return back()->with('error', 'Peserta belum memiliki pendaftaran lomba.');
        }

        // Validate request
        try {
            $validated = $request->validate([
                'jenis_dokumen' => 'required|string|exists:jenis_dokumen,kode_dokumen',
                'file' => 'required|file|mimes:jpeg,jpg,png,pdf,doc,docx|max:10240', // 10MB max
                'keterangan' => 'nullable|string|max:500'
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error('Upload validation failed', [
                'errors' => $e->errors(),
                'request_data' => $request->except('file')
            ]);
            throw $e;
        }

        // Debug logging
        Log::info('Upload attempt', [
            'user_id' => Auth::id(),
            'peserta_id' => $peserta->id_peserta,
            'pendaftaran_id' => $pendaftaran->id_pendaftaran,
            'jenis_dokumen' => $validated['jenis_dokumen'],
            'file_name' => $request->file('file')->getClientOriginalName(),
            'file_size' => $request->file('file')->getSize(),
            'file_mime' => $request->file('file')->getMimeType()
        ]);

        // Get document type info
        $jenisDokumen = JenisDokumen::where('kode_dokumen', $validated['jenis_dokumen'])->first();

        if (!$jenisDokumen) {
            return back()->with('error', 'Jenis dokumen tidak valid.');
        }

        // Map kode_dokumen to allowed enum values for dokumen_peserta table
        $allowedJenisDokumen = [
            'FOTO' => 'foto',
            'KTP' => 'ktp',
            'KK' => 'kartu_keluarga',
            'REKOMENDASI' => 'surat_rekomendasi',
            'IJAZAH' => 'ijazah',
            'SERTIFIKAT' => 'sertifikat'
        ];

        $jenisDokumenEnum = $allowedJenisDokumen[$validated['jenis_dokumen']] ?? 'lainnya';

        // Validate file format if specified
        if ($jenisDokumen->format_file) {
            $allowedFormats = explode(',', $jenisDokumen->format_file);
            $fileExtension = strtolower($request->file('file')->getClientOriginalExtension());

            if (!in_array($fileExtension, array_map('strtolower', $allowedFormats))) {
                return back()->with('error', 'Format file tidak diizinkan. Format yang diizinkan: ' . $jenisDokumen->format_file);
            }
        }

        // Validate file size
        $fileSizeKb = $request->file('file')->getSize() / 1024;
        if ($fileSizeKb > $jenisDokumen->ukuran_max_kb) {
            return back()->with('error', 'Ukuran file terlalu besar. Maksimal: ' . $jenisDokumen->ukuran_max_kb . ' KB');
        }

        // Check if document already exists
        $existingDocument = DokumenPeserta::where('id_pendaftaran', $pendaftaran->id_pendaftaran)
            ->where('jenis_dokumen', $jenisDokumenEnum)
            ->first();

        if ($existingDocument) {
            // Delete old file
            if (Storage::disk('public')->exists($existingDocument->path_file)) {
                Storage::disk('public')->delete($existingDocument->path_file);
            }
            $existingDocument->delete();
        }

        // Store file
        $file = $request->file('file');

        if (!$file || !$file->isValid()) {
            Log::error('Upload failed: Invalid file', [
                'file_error' => $file ? $file->getError() : 'No file received'
            ]);
            return back()->with('error', 'File tidak valid atau rusak.');
        }

        $fileName = time() . '_' . preg_replace('/[^a-zA-Z0-9._-]/', '_', $file->getClientOriginalName());

        try {
            // Ensure directory exists
            $directory = 'dokumen-peserta';
            if (!Storage::disk('public')->exists($directory)) {
                Storage::disk('public')->makeDirectory($directory);
                Log::info('Created directory: ' . $directory);
            }

            Log::info('Attempting to store file', [
                'directory' => $directory,
                'filename' => $fileName,
                'original_name' => $file->getClientOriginalName()
            ]);

            $filePath = $file->storeAs($directory, $fileName, 'public');

            if (!$filePath) {
                Log::error('File storage failed: storeAs returned false');
                return back()->with('error', 'Gagal menyimpan file. Silakan coba lagi.');
            }

            Log::info('File stored successfully', ['path' => $filePath]);

            // Create document record
            $dokumenData = [
                'id_pendaftaran' => $pendaftaran->id_pendaftaran,
                'jenis_dokumen' => $jenisDokumenEnum,
                'nama_file' => $file->getClientOriginalName(),
                'path_file' => $filePath,
                'ukuran_file' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'status_verifikasi' => 'pending',
                'uploaded_by' => Auth::id(),
                'keterangan' => $validated['keterangan']
            ];

            Log::info('Creating document record', $dokumenData);

            $dokumen = DokumenPeserta::create($dokumenData);

            if (!$dokumen) {
                Log::error('Failed to create document record');
                // Delete uploaded file if database insert fails
                Storage::disk('public')->delete($filePath);
                return back()->with('error', 'Gagal menyimpan data dokumen.');
            }

            Log::info('Document uploaded successfully', [
                'dokumen_id' => $dokumen->id_dokumen,
                'file_path' => $filePath
            ]);

        } catch (\Exception $e) {
            Log::error('Upload dokumen gagal: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);

            // Clean up uploaded file if exists
            if (isset($filePath) && Storage::disk('public')->exists($filePath)) {
                Storage::disk('public')->delete($filePath);
            }

            return back()->with('error', 'Gagal mengunggah dokumen: ' . $e->getMessage());
        }

        return back()->with('success', 'Dokumen berhasil diunggah.');
    }

    /**
     * Delete uploaded document
     */
    public function delete(Request $request, Peserta $peserta)
    {
        $adminWilayah = Auth::user()->id_wilayah;

        // Check access for admin daerah
        if ($peserta->id_wilayah !== $adminWilayah) {
            abort(403, 'Akses ditolak');
        }

        $validated = $request->validate([
            'dokumen_id' => 'required|exists:dokumen_peserta,id_dokumen'
        ]);

        $dokumen = DokumenPeserta::findOrFail($validated['dokumen_id']);

        // Verify document belongs to this participant
        if ($dokumen->pendaftaran->peserta->id_peserta !== $peserta->id_peserta) {
            abort(403, 'Akses ditolak');
        }

        // Delete file from storage
        if (Storage::disk('public')->exists($dokumen->path_file)) {
            Storage::disk('public')->delete($dokumen->path_file);
        }

        // Delete record
        $dokumen->delete();

        return back()->with('success', 'Dokumen berhasil dihapus.');
    }

    /**
     * Download document
     */
    public function download(DokumenPeserta $dokumen)
    {
        $adminWilayah = Auth::user()->id_wilayah;

        // Check access for admin daerah
        if ($dokumen->pendaftaran->peserta->id_wilayah !== $adminWilayah) {
            abort(403, 'Akses ditolak');
        }

        if (!Storage::disk('public')->exists($dokumen->path_file)) {
            abort(404, 'File tidak ditemukan.');
        }

        return response()->download(storage_path('app/public/' . $dokumen->path_file), $dokumen->nama_file);
    }

    /**
     * Preview document
     */
    public function preview(DokumenPeserta $dokumen)
    {
        $adminWilayah = Auth::user()->id_wilayah;

        // Check access for admin daerah
        if ($dokumen->pendaftaran->peserta->id_wilayah !== $adminWilayah) {
            abort(403, 'Akses ditolak');
        }

        if (!Storage::disk('public')->exists($dokumen->path_file)) {
            abort(404, 'File tidak ditemukan.');
        }

        return response()->file(storage_path('app/public/' . $dokumen->path_file));
    }

    /**
     * Get document upload statistics
     */
    public function statistics()
    {
        $adminWilayah = Auth::user()->id_wilayah;

        $stats = [
            'total_peserta' => Peserta::where('id_wilayah', $adminWilayah)->count(),
            'peserta_with_documents' => Peserta::where('id_wilayah', $adminWilayah)
                ->whereHas('pendaftaran.dokumenPeserta')
                ->count(),
            'total_documents' => DokumenPeserta::whereHas('pendaftaran.peserta', function($q) use ($adminWilayah) {
                $q->where('id_wilayah', $adminWilayah);
            })->count(),
            'pending_documents' => DokumenPeserta::whereHas('pendaftaran.peserta', function($q) use ($adminWilayah) {
                $q->where('id_wilayah', $adminWilayah);
            })->where('status_verifikasi', 'pending')->count(),
        ];

        return response()->json($stats);
    }
}
