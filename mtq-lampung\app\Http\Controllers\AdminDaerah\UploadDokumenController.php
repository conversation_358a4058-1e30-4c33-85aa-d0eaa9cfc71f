<?php

namespace App\Http\Controllers\AdminDaerah;

use App\Http\Controllers\Controller;
use App\Models\DokumenPeserta;
use App\Models\JenisDokumen;
use App\Models\Pendaftaran;
use App\Models\Peserta;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;
use Inertia\Response;

class UploadDokumenController extends Controller
{
    /**
     * Display a listing of participants for document upload
     */
    public function index(Request $request): Response
    {
        $adminWilayah = Auth::user()->id_wilayah;

        $query = Peserta::with([
            'user',
            'wilayah',
            'pendaftaran.golongan.cabangLomba',
            'pendaftaran.dokumenPeserta'
        ])->where('id_wilayah', $adminWilayah);

        // Search by participant name or registration number
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('nama_lengkap', 'like', "%{$search}%")
                  ->orWhereHas('user', function($subQ) use ($search) {
                      $subQ->where('nama_lengkap', 'like', "%{$search}%");
                  })
                  ->orWhereHas('pendaftaran', function($subQ) use ($search) {
                      $subQ->where('nomor_pendaftaran', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status_peserta', $request->status);
        }

        $peserta = $query->orderBy('created_at', 'desc')->paginate(20);

        return Inertia::render('AdminDaerah/UploadDokumen/Index', [
            'peserta' => $peserta,
            'filters' => $request->only(['search', 'status'])
        ]);
    }

    /**
     * Show upload form for specific participant
     */
    public function show(Peserta $peserta): Response
    {
        $adminWilayah = Auth::user()->id_wilayah;

        // Check access for admin daerah
        if ($peserta->id_wilayah !== $adminWilayah) {
            abort(403, 'Akses ditolak');
        }

        $peserta->load([
            'user',
            'wilayah',
            'pendaftaran.golongan.cabangLomba',
            'pendaftaran.dokumenPeserta'
        ]);

        // Get active document types
        $jenisDokumen = JenisDokumen::aktif()->ordered()->get();

        // Get existing documents for this participant
        $existingDocuments = [];
        if ($peserta->pendaftaran->isNotEmpty()) {
            $pendaftaran = $peserta->pendaftaran->first();
            $existingDocuments = DokumenPeserta::where('id_pendaftaran', $pendaftaran->id_pendaftaran)
                ->get()
                ->keyBy('jenis_dokumen');
        }

        return Inertia::render('AdminDaerah/UploadDokumen/Show', [
            'peserta' => $peserta,
            'jenisDokumen' => $jenisDokumen,
            'existingDocuments' => $existingDocuments
        ]);
    }

    /**
     * Upload document for participant
     */
    public function upload(Request $request, Peserta $peserta)
    {
        $adminWilayah = Auth::user()->id_wilayah;

        // Check access for admin daerah
        if ($peserta->id_wilayah !== $adminWilayah) {
            abort(403, 'Akses ditolak');
        }

        // Get participant's registration
        $pendaftaran = $peserta->pendaftaran->first();
        if (!$pendaftaran) {
            return back()->with('error', 'Peserta belum memiliki pendaftaran lomba.');
        }

        $validated = $request->validate([
            'jenis_dokumen' => 'required|string|exists:jenis_dokumen,kode_dokumen',
            'file' => 'required|file|max:10240', // 10MB max
            'keterangan' => 'nullable|string|max:500'
        ]);

        // Get document type info
        $jenisDokumen = JenisDokumen::where('kode_dokumen', $validated['jenis_dokumen'])->first();

        if (!$jenisDokumen) {
            return back()->with('error', 'Jenis dokumen tidak valid.');
        }

        // Validate file format if specified
        if ($jenisDokumen->format_file) {
            $allowedFormats = explode(',', $jenisDokumen->format_file);
            $fileExtension = strtolower($request->file('file')->getClientOriginalExtension());

            if (!in_array($fileExtension, array_map('strtolower', $allowedFormats))) {
                return back()->with('error', 'Format file tidak diizinkan. Format yang diizinkan: ' . $jenisDokumen->format_file);
            }
        }

        // Validate file size
        $fileSizeKb = $request->file('file')->getSize() / 1024;
        if ($fileSizeKb > $jenisDokumen->ukuran_max_kb) {
            return back()->with('error', 'Ukuran file terlalu besar. Maksimal: ' . $jenisDokumen->ukuran_max_kb . ' KB');
        }

        // Check if document already exists
        $existingDocument = DokumenPeserta::where('id_pendaftaran', $pendaftaran->id_pendaftaran)
            ->where('jenis_dokumen', $validated['jenis_dokumen'])
            ->first();

        if ($existingDocument) {
            // Delete old file
            if (Storage::disk('public')->exists($existingDocument->path_file)) {
                Storage::disk('public')->delete($existingDocument->path_file);
            }
            $existingDocument->delete();
        }

        // Store file
        $file = $request->file('file');
        $fileName = time() . '_' . $file->getClientOriginalName();
        $filePath = $file->storeAs('dokumen-peserta', $fileName, 'public');

        // Create document record
        DokumenPeserta::create([
            'id_pendaftaran' => $pendaftaran->id_pendaftaran,
            'jenis_dokumen' => $validated['jenis_dokumen'],
            'nama_file' => $file->getClientOriginalName(),
            'path_file' => $filePath,
            'ukuran_file' => $file->getSize(),
            'mime_type' => $file->getMimeType(),
            'status_verifikasi' => 'pending',
            'uploaded_by' => Auth::id(),
            'keterangan' => $validated['keterangan']
        ]);

        return back()->with('success', 'Dokumen berhasil diunggah.');
    }

    /**
     * Delete uploaded document
     */
    public function delete(Request $request, Peserta $peserta)
    {
        $adminWilayah = Auth::user()->id_wilayah;

        // Check access for admin daerah
        if ($peserta->id_wilayah !== $adminWilayah) {
            abort(403, 'Akses ditolak');
        }

        $validated = $request->validate([
            'dokumen_id' => 'required|exists:dokumen_peserta,id_dokumen'
        ]);

        $dokumen = DokumenPeserta::findOrFail($validated['dokumen_id']);

        // Verify document belongs to this participant
        if ($dokumen->pendaftaran->peserta->id_peserta !== $peserta->id_peserta) {
            abort(403, 'Akses ditolak');
        }

        // Delete file from storage
        if (Storage::disk('public')->exists($dokumen->path_file)) {
            Storage::disk('public')->delete($dokumen->path_file);
        }

        // Delete record
        $dokumen->delete();

        return back()->with('success', 'Dokumen berhasil dihapus.');
    }

    /**
     * Download document
     */
    public function download(DokumenPeserta $dokumen)
    {
        $adminWilayah = Auth::user()->id_wilayah;

        // Check access for admin daerah
        if ($dokumen->pendaftaran->peserta->id_wilayah !== $adminWilayah) {
            abort(403, 'Akses ditolak');
        }

        if (!Storage::disk('public')->exists($dokumen->path_file)) {
            abort(404, 'File tidak ditemukan.');
        }

        return Storage::disk('public')->download($dokumen->path_file, $dokumen->nama_file);
    }

    /**
     * Preview document
     */
    public function preview(DokumenPeserta $dokumen)
    {
        $adminWilayah = Auth::user()->id_wilayah;

        // Check access for admin daerah
        if ($dokumen->pendaftaran->peserta->id_wilayah !== $adminWilayah) {
            abort(403, 'Akses ditolak');
        }

        if (!Storage::disk('public')->exists($dokumen->path_file)) {
            abort(404, 'File tidak ditemukan.');
        }

        return response()->file(storage_path('app/public/' . $dokumen->path_file));
    }

    /**
     * Get document upload statistics
     */
    public function statistics()
    {
        $adminWilayah = Auth::user()->id_wilayah;

        $stats = [
            'total_peserta' => Peserta::where('id_wilayah', $adminWilayah)->count(),
            'peserta_with_documents' => Peserta::where('id_wilayah', $adminWilayah)
                ->whereHas('pendaftaran.dokumenPeserta')
                ->count(),
            'total_documents' => DokumenPeserta::whereHas('pendaftaran.peserta', function($q) use ($adminWilayah) {
                $q->where('id_wilayah', $adminWilayah);
            })->count(),
            'pending_documents' => DokumenPeserta::whereHas('pendaftaran.peserta', function($q) use ($adminWilayah) {
                $q->where('id_wilayah', $adminWilayah);
            })->where('status_verifikasi', 'pending')->count(),
        ];

        return response()->json($stats);
    }
}
