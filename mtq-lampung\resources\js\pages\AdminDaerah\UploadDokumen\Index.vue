<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue'
import { Head, router, useForm } from '@inertiajs/vue3'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import Icon from '@/components/Icon.vue'
import TextLink from '@/components/TextLink.vue'

interface User {
  nama_lengkap: string
}

interface Wilayah {
  nama_wilayah: string
}

interface CabangLomba {
  nama_cabang: string
}

interface Golongan {
  nama_golongan: string
  cabang_lomba: CabangLomba
}

interface DokumenPeserta {
  id_dokumen: number
  jenis_dokumen: string
  nama_file: string
  status_verifikasi: string
  created_at: string
}

interface Pendaftaran {
  id_pendaftaran: number
  nomor_pendaftaran: string
  status_pendaftaran: string
  golongan: Golongan
  dokumen_peserta: DokumenPeserta[]
}

interface Peserta {
  id_peserta: number
  nama_lengkap: string
  status_peserta: string
  created_at: string
  user: User
  wilayah: Wilayah
  pendaftaran: Pendaftaran[]
}

interface PaginatedData {
  data: Peserta[]
  current_page: number
  last_page: number
  per_page: number
  total: number
  links: Array<{
    url: string | null
    label: string
    active: boolean
  }>
}

const props = defineProps<{
  peserta: PaginatedData
  filters: {
    search?: string
    status?: string
  }
}>()

const searchForm = useForm({
  search: props.filters.search || '',
  status: props.filters.status || ''
})

function search() {
  searchForm.get(route('admin-daerah.upload-dokumen.index'), {
    preserveState: true,
    replace: true
  })
}

function clearFilters() {
  searchForm.reset()
  search()
}

function getStatusBadgeVariant(status: string) {
  const variants: Record<string, string> = {
    'pending': 'secondary',
    'approved': 'default',
    'verified': 'default',
    'rejected': 'destructive'
  }
  return variants[status] || 'secondary'
}

function getStatusLabel(status: string) {
  const labels: Record<string, string> = {
    'pending': 'Menunggu',
    'approved': 'Disetujui',
    'verified': 'Terverifikasi',
    'rejected': 'Ditolak'
  }
  return labels[status] || status
}

function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

function getDocumentCount(peserta: Peserta): number {
  if (peserta.pendaftaran.length === 0) return 0
  return peserta.pendaftaran[0].dokumen_peserta?.length || 0
}

function getPendingDocumentCount(peserta: Peserta): number {
  if (peserta.pendaftaran.length === 0) return 0
  const dokumen = peserta.pendaftaran[0].dokumen_peserta || []
  return dokumen.filter(doc => doc.status_verifikasi === 'pending').length
}
</script>

<template>
  <AppLayout>
    <Head title="Upload Dokumen Peserta" />

    <div class="space-y-6">
      <!-- Header -->
      <div class="flex items-center justify-between">
        <div class="islamic-gradient p-6 rounded-lg islamic-shadow">
          <Heading title="Upload Dokumen Peserta" class="text-white"/>
          <p class="text-green-100 mt-2">Kelola upload dokumen untuk peserta MTQ di wilayah Anda</p>
        </div>
      </div>

      <!-- Filters -->
      <Card class="islamic-shadow">
        <CardHeader>
          <CardTitle class="text-islamic-700">Filter & Pencarian</CardTitle>
        </CardHeader>
        <CardContent>
          <form @submit.prevent="search" class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label for="search">Pencarian</Label>
              <Input
                id="search"
                v-model="searchForm.search"
                placeholder="Cari nama peserta atau nomor pendaftaran..."
                class="mt-1"
              />
            </div>
            <div>
              <Label for="status">Status Peserta</Label>
              <Select v-model="searchForm.status">
                <SelectTrigger class="mt-1">
                  <SelectValue placeholder="Semua Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Status</SelectItem>
                  <SelectItem value="pending">Menunggu</SelectItem>
                  <SelectItem value="approved">Disetujui</SelectItem>
                  <SelectItem value="verified">Terverifikasi</SelectItem>
                  <SelectItem value="rejected">Ditolak</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div class="flex items-end gap-2">
              <Button type="submit" :disabled="searchForm.processing">
                <Icon name="search" class="w-4 h-4 mr-2" />
                Cari
              </Button>
              <Button type="button" variant="outline" @click="clearFilters">
                <Icon name="x" class="w-4 h-4 mr-2" />
                Reset
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      <!-- Data Table -->
      <Card class="islamic-shadow">
        <CardHeader>
          <CardTitle class="text-islamic-700">Daftar Peserta</CardTitle>
          <CardDescription>
            Total: {{ peserta.total }} peserta
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div class="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Peserta</TableHead>
                  <TableHead>Pendaftaran</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Dokumen</TableHead>
                  <TableHead>Tanggal Daftar</TableHead>
                  <TableHead class="text-right">Aksi</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow v-for="item in peserta.data" :key="item.id_peserta">
                  <TableCell>
                    <div>
                      <div class="font-medium">{{ item.nama_lengkap }}</div>
                      <div class="text-sm text-muted-foreground">
                        {{ item.wilayah.nama_wilayah }}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div v-if="item.pendaftaran.length > 0">
                      <div class="font-medium">{{ item.pendaftaran[0].nomor_pendaftaran }}</div>
                      <div class="text-sm text-muted-foreground">
                        {{ item.pendaftaran[0].golongan.nama_golongan }}
                      </div>
                      <div class="text-sm text-muted-foreground">
                        {{ item.pendaftaran[0].golongan.cabang_lomba.nama_cabang }}
                      </div>
                    </div>
                    <span v-else class="text-muted-foreground">Belum mendaftar</span>
                  </TableCell>
                  <TableCell>
                    <Badge :variant="getStatusBadgeVariant(item.status_peserta)">
                      {{ getStatusLabel(item.status_peserta) }}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div class="flex items-center gap-2">
                      <Badge variant="outline">
                        {{ getDocumentCount(item) }} dokumen
                      </Badge>
                      <Badge v-if="getPendingDocumentCount(item) > 0" variant="secondary">
                        {{ getPendingDocumentCount(item) }} pending
                      </Badge>
                    </div>
                  </TableCell>
                  <TableCell>{{ formatDate(item.created_at) }}</TableCell>
                  <TableCell class="text-right">
                    <div class="flex items-center justify-end gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        as-child
                        :disabled="item.pendaftaran.length === 0"
                      >
                        <TextLink :href="route('admin-daerah.upload-dokumen.show', item.id_peserta)">
                          <Icon name="upload" class="w-4 h-4 mr-1" />
                          Upload
                        </TextLink>
                      </Button>
                      <Button variant="outline" size="sm" as-child>
                        <TextLink :href="route('admin-daerah.peserta.show', item.id_peserta)">
                          <Icon name="eye" class="w-4 h-4" />
                        </TextLink>
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>

          <!-- Pagination -->
          <div v-if="peserta.last_page > 1" class="mt-4 flex justify-center">
            <div class="flex items-center gap-2">
              <Button
                v-for="link in peserta.links"
                :key="link.label"
                :variant="link.active ? 'default' : 'outline'"
                size="sm"
                :disabled="!link.url"
                @click="link.url && router.visit(link.url)"
                v-html="link.label"
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </AppLayout>
</template>
