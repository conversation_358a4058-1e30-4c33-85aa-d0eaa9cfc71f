<?php

namespace App\Http\Controllers\AdminDaerah;

use App\Http\Controllers\Controller;
use App\Models\JenisDokumen;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class JenisDokumenController extends Controller
{
    /**
     * Display a listing of document types
     */
    public function index(Request $request): Response
    {
        $query = JenisDokumen::query();

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by category
        if ($request->filled('kategori')) {
            $query->where('kategori', $request->kategori);
        }

        // Search by name or code
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('nama_dokumen', 'like', "%{$search}%")
                  ->orWhere('kode_dokumen', 'like', "%{$search}%");
            });
        }

        $jenisDokumen = $query->ordered()->paginate(20);

        return Inertia::render('AdminDaerah/JenisDokumen/Index', [
            'jenisDokumen' => $jenisDokumen,
            'filters' => $request->only(['status', 'kategori', 'search']),
            'kategoriOptions' => [
                'identitas' => 'Identitas',
                'pendidikan' => 'Pendidikan',
                'rekomendasi' => 'Rekomendasi',
                'lainnya' => 'Lainnya'
            ]
        ]);
    }

    /**
     * Show the form for creating a new document type
     */
    public function create(): Response
    {
        return Inertia::render('AdminDaerah/JenisDokumen/Create');
    }

    /**
     * Store a newly created document type
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'kode_dokumen' => 'required|string|max:20|unique:jenis_dokumen,kode_dokumen',
            'nama_dokumen' => 'required|string|max:100',
            'deskripsi' => 'nullable|string',
            'kategori' => 'required|in:identitas,pendidikan,rekomendasi,lainnya',
            'wajib' => 'boolean',
            'format_file' => 'nullable|string',
            'ukuran_max_kb' => 'required|integer|min:100|max:10240',
            'status' => 'required|in:aktif,non_aktif',
            'urutan' => 'required|integer|min:0'
        ]);

        JenisDokumen::create($validated);

        return redirect()->route('admin-daerah.jenis-dokumen.index')
            ->with('success', 'Jenis dokumen berhasil ditambahkan.');
    }

    /**
     * Display the specified document type
     */
    public function show(JenisDokumen $jenisDokumen): Response
    {
        $jenisDokumen->load('verifikasiDokumen');

        return Inertia::render('AdminDaerah/JenisDokumen/Show', [
            'jenisDokumen' => $jenisDokumen
        ]);
    }

    /**
     * Show the form for editing the specified document type
     */
    public function edit(JenisDokumen $jenisDokumen): Response
    {
        return Inertia::render('AdminDaerah/JenisDokumen/Edit', [
            'jenisDokumen' => $jenisDokumen
        ]);
    }

    /**
     * Update the specified document type
     */
    public function update(Request $request, JenisDokumen $jenisDokumen)
    {
        $validated = $request->validate([
            'kode_dokumen' => 'required|string|max:20|unique:jenis_dokumen,kode_dokumen,' . $jenisDokumen->id_jenis_dokumen . ',id_jenis_dokumen',
            'nama_dokumen' => 'required|string|max:100',
            'deskripsi' => 'nullable|string',
            'kategori' => 'required|in:identitas,pendidikan,rekomendasi,lainnya',
            'wajib' => 'boolean',
            'format_file' => 'nullable|string',
            'ukuran_max_kb' => 'required|integer|min:100|max:10240',
            'status' => 'required|in:aktif,non_aktif',
            'urutan' => 'required|integer|min:0'
        ]);

        $jenisDokumen->update($validated);

        return redirect()->route('admin-daerah.jenis-dokumen.index')
            ->with('success', 'Jenis dokumen berhasil diperbarui.');
    }

    /**
     * Remove the specified document type
     */
    public function destroy(JenisDokumen $jenisDokumen)
    {
        // Check if document type is being used
        if ($jenisDokumen->verifikasiDokumen()->exists()) {
            return back()->with('error', 'Jenis dokumen tidak dapat dihapus karena sedang digunakan.');
        }

        $jenisDokumen->delete();

        return redirect()->route('admin-daerah.jenis-dokumen.index')
            ->with('success', 'Jenis dokumen berhasil dihapus.');
    }

    /**
     * Toggle status of document type
     */
    public function toggleStatus(JenisDokumen $jenisDokumen)
    {
        $newStatus = $jenisDokumen->status === 'aktif' ? 'non_aktif' : 'aktif';
        $jenisDokumen->update(['status' => $newStatus]);

        $message = $newStatus === 'aktif' 
            ? 'Jenis dokumen berhasil diaktifkan.' 
            : 'Jenis dokumen berhasil dinonaktifkan.';

        return back()->with('success', $message);
    }

    /**
     * Bulk update document types order
     */
    public function updateOrder(Request $request)
    {
        $validated = $request->validate([
            'items' => 'required|array',
            'items.*.id' => 'required|exists:jenis_dokumen,id_jenis_dokumen',
            'items.*.urutan' => 'required|integer|min:0'
        ]);

        foreach ($validated['items'] as $item) {
            JenisDokumen::where('id_jenis_dokumen', $item['id'])
                ->update(['urutan' => $item['urutan']]);
        }

        return back()->with('success', 'Urutan jenis dokumen berhasil diperbarui.');
    }
}
