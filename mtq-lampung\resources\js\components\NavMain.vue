<script setup lang="ts">
import { SidebarGroup, SidebarGroupLabel, SidebarMenu, SidebarMenuButton, SidebarMenuItem, SidebarMenuSub, SidebarMenuSubButton, SidebarMenuSubItem } from '@/components/ui/sidebar';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { type NavItem } from '@/types';
import { Link, usePage } from '@inertiajs/vue3';
import { ChevronRight } from 'lucide-vue-next';
import { ref } from 'vue';

defineProps<{
    items: NavItem[];
}>();

const page = usePage();
const openGroups = ref<Set<string>>(new Set());

function toggleGroup(groupTitle: string) {
    if (openGroups.value.has(groupTitle)) {
        openGroups.value.delete(groupTitle);
    } else {
        openGroups.value.add(groupTitle);
    }
}

function isGroupOpen(groupTitle: string) {
    return openGroups.value.has(groupTitle);
}

function isItemActive(item: NavItem): boolean {
    if (item.href && item.href === page.url) {
        return true;
    }
    if (item.items) {
        return item.items.some(subItem => subItem.href === page.url);
    }
    return false;
}
</script>

<template>
    <SidebarGroup class="px-2 py-0">
        <SidebarGroupLabel>Platform</SidebarGroupLabel>
        <SidebarMenu>
            <SidebarMenuItem v-for="item in items" :key="item.title">
                <!-- Regular menu item (no subitems) -->
                <SidebarMenuButton
                    v-if="!item.items || item.items.length === 0"
                    as-child
                    :is-active="item.href === page.url"
                    :tooltip="item.title"
                >
                    <Link :href="item.href || '#'">
                        <component :is="item.icon" v-if="item.icon" />
                        <span>{{ item.title }}</span>
                    </Link>
                </SidebarMenuButton>

                <!-- Collapsible menu group -->
                <Collapsible v-else :open="isGroupOpen(item.title)" @update:open="() => toggleGroup(item.title)">
                    <CollapsibleTrigger as-child>
                        <SidebarMenuButton
                            :tooltip="item.title"
                            :is-active="isItemActive(item)"
                            class="group/collapsible"
                        >
                            <component :is="item.icon" v-if="item.icon" />
                            <span>{{ item.title }}</span>
                            <ChevronRight
                                class="ml-auto h-4 w-4 transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90"
                            />
                        </SidebarMenuButton>
                    </CollapsibleTrigger>
                    <CollapsibleContent>
                        <SidebarMenuSub>
                            <SidebarMenuSubItem v-for="subItem in item.items" :key="subItem.title">
                                <SidebarMenuSubButton
                                    as-child
                                    :is-active="subItem.href === page.url"
                                >
                                    <Link :href="subItem.href">
                                        <component :is="subItem.icon" v-if="subItem.icon" />
                                        <span>{{ subItem.title }}</span>
                                    </Link>
                                </SidebarMenuSubButton>
                            </SidebarMenuSubItem>
                        </SidebarMenuSub>
                    </CollapsibleContent>
                </Collapsible>
            </SidebarMenuItem>
        </SidebarMenu>
    </SidebarGroup>
</template>
