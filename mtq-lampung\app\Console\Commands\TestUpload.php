<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Peserta;
use App\Models\JenisDokumen;
use App\Models\DokumenPeserta;
use Illuminate\Support\Facades\Storage;

class TestUpload extends Command
{
    protected $signature = 'test:upload';
    protected $description = 'Test upload functionality';

    public function handle()
    {
        $this->info('Testing upload functionality...');

        // Check if storage directory exists
        $this->info('Checking storage directory...');
        $storagePath = storage_path('app/public/dokumen-peserta');
        $this->info('Storage path: ' . $storagePath);

        if (!Storage::disk('public')->exists('dokumen-peserta')) {
            Storage::disk('public')->makeDirectory('dokumen-peserta');
            $this->info('Created dokumen-peserta directory');
        } else {
            $this->info('dokumen-peserta directory exists');
        }

        // Check directory permissions
        if (is_writable($storagePath)) {
            $this->info('Directory is writable');
        } else {
            $this->error('Directory is NOT writable');
        }

        // Check jenis dokumen
        $this->info('Checking jenis dokumen...');
        try {
            $jenisDokumen = JenisDokumen::where('status', 'aktif')->get();
            $this->info('Found ' . $jenisDokumen->count() . ' active document types');
            foreach ($jenisDokumen as $jenis) {
                $this->line('- ' . $jenis->kode_dokumen . ': ' . $jenis->nama_dokumen);
            }
        } catch (\Exception $e) {
            $this->error('Error fetching jenis dokumen: ' . $e->getMessage());
        }

        // Check peserta
        $this->info('Checking peserta...');
        try {
            $peserta = Peserta::with('pendaftaran')->first();
            if ($peserta) {
                $this->info('Found peserta: ' . $peserta->nama_lengkap);
                $this->info('Peserta ID: ' . $peserta->id_peserta);
                $this->info('Wilayah ID: ' . $peserta->id_wilayah);

                if ($peserta->pendaftaran->isNotEmpty()) {
                    $pendaftaran = $peserta->pendaftaran->first();
                    $this->info('Peserta has pendaftaran: ' . $pendaftaran->nomor_pendaftaran);
                    $this->info('Pendaftaran ID: ' . $pendaftaran->id_pendaftaran);
                } else {
                    $this->warn('Peserta has no pendaftaran');
                }
            } else {
                $this->warn('No peserta found');
            }
        } catch (\Exception $e) {
            $this->error('Error fetching peserta: ' . $e->getMessage());
        }

        // Check existing documents
        $this->info('Checking existing documents...');
        try {
            $documents = DokumenPeserta::count();
            $this->info('Found ' . $documents . ' existing documents');
        } catch (\Exception $e) {
            $this->error('Error fetching documents: ' . $e->getMessage());
        }

        // Test file creation
        $this->info('Testing file creation...');
        try {
            $testContent = 'Test file content - ' . now();
            $testPath = 'dokumen-peserta/test_' . time() . '.txt';

            if (Storage::disk('public')->put($testPath, $testContent)) {
                $this->info('Test file created successfully: ' . $testPath);

                // Clean up test file
                Storage::disk('public')->delete($testPath);
                $this->info('Test file deleted');
            } else {
                $this->error('Failed to create test file');
            }
        } catch (\Exception $e) {
            $this->error('Error testing file creation: ' . $e->getMessage());
        }

        $this->info('Test completed!');
    }
}
