<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Peserta;
use App\Models\JenisDokumen;
use App\Models\DokumenPeserta;
use Illuminate\Support\Facades\Storage;

class TestUpload extends Command
{
    protected $signature = 'test:upload';
    protected $description = 'Test upload functionality';

    public function handle()
    {
        $this->info('Testing upload functionality...');

        // Check if storage directory exists
        $this->info('Checking storage directory...');
        if (!Storage::disk('public')->exists('dokumen-peserta')) {
            Storage::disk('public')->makeDirectory('dokumen-peserta');
            $this->info('Created dokumen-peserta directory');
        } else {
            $this->info('dokumen-peserta directory exists');
        }

        // Check jenis dokumen
        $this->info('Checking jenis dokumen...');
        $jenisDokumen = JenisDokumen::where('status', 'aktif')->get();
        $this->info('Found ' . $jenisDokumen->count() . ' active document types');
        foreach ($jenisDokumen as $jenis) {
            $this->line('- ' . $jenis->kode_dokumen . ': ' . $jenis->nama_dokumen);
        }

        // Check peserta
        $this->info('Checking peserta...');
        $peserta = Peserta::with('pendaftaran')->first();
        if ($peserta) {
            $this->info('Found peserta: ' . $peserta->nama_lengkap);
            if ($peserta->pendaftaran->isNotEmpty()) {
                $this->info('Peserta has pendaftaran: ' . $peserta->pendaftaran->first()->nomor_pendaftaran);
            } else {
                $this->warn('Peserta has no pendaftaran');
            }
        } else {
            $this->warn('No peserta found');
        }

        // Check existing documents
        $this->info('Checking existing documents...');
        $documents = DokumenPeserta::count();
        $this->info('Found ' . $documents . ' existing documents');

        $this->info('Test completed!');
    }
}
