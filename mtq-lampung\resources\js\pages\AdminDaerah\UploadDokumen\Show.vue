<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue'
import { Head, useForm } from '@inertiajs/vue3'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import Icon from '@/components/Icon.vue'
import TextLink from '@/components/TextLink.vue'
import { ref } from 'vue'

interface User {
  nama_lengkap: string
}

interface Wilayah {
  nama_wilayah: string
}

interface CabangLomba {
  nama_cabang: string
}

interface Golongan {
  nama_golongan: string
  cabang_lomba: CabangLomba
}

interface DokumenPeserta {
  id_dokumen: number
  jenis_dokumen: string
  nama_file: string
  ukuran_file: number
  status_verifikasi: string
  created_at: string
  keterangan: string | null
}

interface Pendaftaran {
  id_pendaftaran: number
  nomor_pendaftaran: string
  status_pendaftaran: string
  golongan: Golongan
  dokumen_peserta: DokumenPeserta[]
}

interface Peserta {
  id_peserta: number
  nama_lengkap: string
  status_peserta: string
  user: User
  wilayah: Wilayah
  pendaftaran: Pendaftaran[]
}

interface JenisDokumen {
  id_jenis_dokumen: number
  kode_dokumen: string
  nama_dokumen: string
  deskripsi: string | null
  kategori: string
  wajib: boolean
  format_file: string | null
  ukuran_max_kb: number
  status: string
}

const props = defineProps<{
  peserta: Peserta
  jenisDokumen: JenisDokumen[]
  existingDocuments: Record<string, DokumenPeserta>
}>()

const uploadForm = useForm({
  jenis_dokumen: '',
  file: null as File | null,
  keterangan: ''
})

const deleteForm = useForm({
  dokumen_id: 0
})

const showPreviewDialog = ref(false)
const showDeleteDialog = ref(false)
const selectedDocument = ref<DokumenPeserta | null>(null)
const previewUrl = ref('')

function uploadDocument() {
  uploadForm.post(route('admin-daerah.upload-dokumen.upload', props.peserta.id_peserta), {
    onSuccess: () => {
      uploadForm.reset()
    }
  })
}

function confirmDelete(dokumen: DokumenPeserta) {
  selectedDocument.value = dokumen
  deleteForm.dokumen_id = dokumen.id_dokumen
  showDeleteDialog.value = true
}

function deleteDocument() {
  deleteForm.post(route('admin-daerah.upload-dokumen.delete', props.peserta.id_peserta), {
    onSuccess: () => {
      showDeleteDialog.value = false
      selectedDocument.value = null
    }
  })
}

function openPreview(dokumen: DokumenPeserta) {
  selectedDocument.value = dokumen
  previewUrl.value = route('admin-daerah.upload-dokumen.preview', dokumen.id_dokumen)
  showPreviewDialog.value = true
}

function getStatusBadgeVariant(status: string) {
  const variants: Record<string, string> = {
    'pending': 'secondary',
    'approved': 'default',
    'rejected': 'destructive'
  }
  return variants[status] || 'secondary'
}

function getStatusLabel(status: string) {
  const labels: Record<string, string> = {
    'pending': 'Menunggu',
    'approved': 'Disetujui',
    'rejected': 'Ditolak'
  }
  return labels[status] || status
}

function getKategoriBadgeVariant(kategori: string) {
  const variants: Record<string, string> = {
    'identitas': 'default',
    'pendidikan': 'secondary',
    'rekomendasi': 'outline',
    'lainnya': 'destructive'
  }
  return variants[kategori] || 'default'
}

function formatFileSize(sizeBytes: number): string {
  const sizeKb = sizeBytes / 1024
  if (sizeKb >= 1024) {
    return `${(sizeKb / 1024).toFixed(1)} MB`
  }
  return `${sizeKb.toFixed(1)} KB`
}

function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

function isImageFile(fileName: string): boolean {
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
  const extension = fileName.split('.').pop()?.toLowerCase()
  return imageExtensions.includes(extension || '')
}

function isPdfFile(fileName: string): boolean {
  return fileName.toLowerCase().endsWith('.pdf')
}

const kategoriOptions: Record<string, string> = {
  'identitas': 'Identitas',
  'pendidikan': 'Pendidikan',
  'rekomendasi': 'Rekomendasi',
  'lainnya': 'Lainnya'
}
</script>

<template>
  <AppLayout>
    <Head :title="`Upload Dokumen - ${peserta.nama_lengkap}`" />

    <div class="space-y-6">
      <!-- Header -->
      <div class="flex items-center justify-between">
        <div class="islamic-gradient p-6 rounded-lg islamic-shadow">
          <Heading title="Upload Dokumen Peserta" class="text-white"/>
          <p class="text-green-100 mt-2">{{ peserta.nama_lengkap }} - {{ peserta.wilayah.nama_wilayah }}</p>
        </div>
        <Button variant="outline" as-child class="islamic-shadow">
          <TextLink :href="route('admin-daerah.upload-dokumen.index')">
            <Icon name="arrow-left" class="w-4 h-4 mr-2" />
            Kembali
          </TextLink>
        </Button>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Participant Information -->
        <div class="lg:col-span-1">
          <Card class="islamic-shadow">
            <CardHeader>
              <CardTitle class="text-islamic-700">Informasi Peserta</CardTitle>
            </CardHeader>
            <CardContent class="space-y-4">
              <div>
                <Label class="text-sm font-medium text-muted-foreground">Nama Lengkap</Label>
                <p class="text-lg mt-1">{{ peserta.nama_lengkap }}</p>
              </div>

              <div>
                <Label class="text-sm font-medium text-muted-foreground">Wilayah</Label>
                <p class="text-lg mt-1">{{ peserta.wilayah.nama_wilayah }}</p>
              </div>

              <div>
                <Label class="text-sm font-medium text-muted-foreground">Status</Label>
                <div class="mt-1">
                  <Badge :variant="getStatusBadgeVariant(peserta.pendaftaran[0].status_pendaftaran)">
                    {{ getStatusLabel(peserta.pendaftaran[0].status_pendaftaran) }}
                  </Badge>
                </div>
              </div>

              <div v-if="peserta.pendaftaran.length > 0">
                <Label class="text-sm font-medium text-muted-foreground">Nomor Pendaftaran</Label>
                <p class="text-lg mt-1">{{ peserta.pendaftaran[0].nomor_pendaftaran }}</p>
              </div>

              <div v-if="peserta.pendaftaran.length > 0">
                <Label class="text-sm font-medium text-muted-foreground">Cabang Lomba</Label>
                <p class="text-lg mt-1">{{ peserta.pendaftaran[0].golongan.cabang_lomba.nama_cabang }}</p>
              </div>

              <div v-if="peserta.pendaftaran.length > 0">
                <Label class="text-sm font-medium text-muted-foreground">Golongan</Label>
                <p class="text-lg mt-1">{{ peserta.pendaftaran[0].golongan.nama_golongan }}</p>
              </div>
            </CardContent>
          </Card>
        </div>

        <!-- Upload Form & Document List -->
        <div class="lg:col-span-2 space-y-6">
          <!-- Upload Form -->
          <Card class="islamic-shadow">
            <CardHeader>
              <CardTitle class="text-islamic-700">Upload Dokumen Baru</CardTitle>
              <CardDescription>
                Pilih jenis dokumen dan unggah file untuk peserta ini
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form @submit.prevent="uploadDocument" class="space-y-4">
                <div>
                  <Label for="jenis_dokumen">Jenis Dokumen *</Label>
                  <Select v-model="uploadForm.jenis_dokumen" required>
                    <SelectTrigger class="mt-1">
                      <SelectValue placeholder="Pilih jenis dokumen" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem
                        v-for="jenis in jenisDokumen"
                        :key="jenis.kode_dokumen"
                        :value="jenis.kode_dokumen"
                        :disabled="existingDocuments[jenis.kode_dokumen] !== undefined"
                      >
                        <div class="flex items-center justify-between w-full">
                          <span>{{ jenis.nama_dokumen }}</span>
                          <div class="flex items-center gap-2 ml-2">
                            <Badge :variant="getKategoriBadgeVariant(jenis.kategori)" class="text-xs">
                              {{ kategoriOptions[jenis.kategori] }}
                            </Badge>
                            <Badge v-if="jenis.wajib" variant="destructive" class="text-xs">
                              Wajib
                            </Badge>
                            <Badge v-if="existingDocuments[jenis.kode_dokumen]" variant="outline" class="text-xs">
                              Sudah ada
                            </Badge>
                          </div>
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <p v-if="uploadForm.errors.jenis_dokumen" class="text-sm text-red-600 mt-1">
                    {{ uploadForm.errors.jenis_dokumen }}
                  </p>
                </div>

                <div>
                  <Label for="file">File Dokumen *</Label>
                  <Input
                    id="file"
                    type="file"
                    @input="uploadForm.file = ($event.target as HTMLInputElement).files?.[0] || null"
                    class="mt-1"
                    required
                  />
                  <p v-if="uploadForm.errors.file" class="text-sm text-red-600 mt-1">
                    {{ uploadForm.errors.file }}
                  </p>
                  <p class="text-sm text-muted-foreground mt-1">
                    Maksimal 10MB. Format yang diizinkan tergantung jenis dokumen.
                  </p>
                </div>

                <div>
                  <Label for="keterangan">Keterangan</Label>
                  <Textarea
                    id="keterangan"
                    v-model="uploadForm.keterangan"
                    placeholder="Keterangan tambahan (opsional)"
                    class="mt-1"
                    rows="3"
                  />
                  <p v-if="uploadForm.errors.keterangan" class="text-sm text-red-600 mt-1">
                    {{ uploadForm.errors.keterangan }}
                  </p>
                </div>

                <Button type="submit" :disabled="uploadForm.processing" class="w-full islamic-gradient">
                  <Icon name="upload" class="w-4 h-4 mr-2" />
                  {{ uploadForm.processing ? 'Mengunggah...' : 'Upload Dokumen' }}
                </Button>
              </form>
            </CardContent>
          </Card>

          <!-- Existing Documents -->
          <Card class="islamic-shadow">
            <CardHeader>
              <CardTitle class="text-islamic-700">Dokumen yang Sudah Diunggah</CardTitle>
              <CardDescription>
                Daftar dokumen yang telah diunggah untuk peserta ini
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div v-if="Object.keys(existingDocuments).length === 0" class="text-center py-8">
                <Icon name="file-x" class="w-12 h-12 mx-auto text-gray-400 mb-4" />
                <p class="text-gray-600">Belum ada dokumen yang diunggah</p>
              </div>

              <div v-else class="space-y-4">
                <div
                  v-for="dokumen in Object.values(existingDocuments)"
                  :key="dokumen.id_dokumen"
                  class="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                >
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <div class="flex items-center gap-2 mb-2">
                        <h4 class="font-medium">{{ dokumen.jenis_dokumen }}</h4>
                        <Badge :variant="getStatusBadgeVariant(dokumen.status_verifikasi)">
                          {{ getStatusLabel(dokumen.status_verifikasi) }}
                        </Badge>
                      </div>

                      <p class="text-sm text-gray-600 mb-1">{{ dokumen.nama_file }}</p>
                      <p class="text-sm text-gray-500 mb-2">
                        {{ formatFileSize(dokumen.ukuran_file) }} • {{ formatDate(dokumen.created_at) }}
                      </p>

                      <p v-if="dokumen.keterangan" class="text-sm text-gray-700">
                        {{ dokumen.keterangan }}
                      </p>
                    </div>

                    <div class="flex items-center gap-2 ml-4">
                      <Button variant="outline" size="sm" @click="openPreview(dokumen)">
                        <Icon name="eye" class="w-4 h-4" />
                      </Button>
                      <Button variant="outline" size="sm" as-child>
                        <a :href="route('admin-daerah.upload-dokumen.download', dokumen.id_dokumen)" target="_blank">
                          <Icon name="download" class="w-4 h-4" />
                        </a>
                      </Button>
                      <Button variant="destructive" size="sm" @click="confirmDelete(dokumen)">
                        <Icon name="trash" class="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>

    <!-- Preview Dialog -->
    <Dialog v-model:open="showPreviewDialog">
      <DialogContent class="max-w-4xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle>Preview Dokumen</DialogTitle>
          <DialogDescription>
            {{ selectedDocument?.nama_file }}
          </DialogDescription>
        </DialogHeader>
        <div class="flex justify-center items-center min-h-[400px] bg-gray-50 rounded-lg">
          <!-- Image Preview -->
          <img
            v-if="selectedDocument && isImageFile(selectedDocument.nama_file)"
            :src="previewUrl"
            :alt="selectedDocument.nama_file"
            class="max-w-full max-h-[60vh] object-contain"
          />

          <!-- PDF Preview -->
          <iframe
            v-else-if="selectedDocument && isPdfFile(selectedDocument.nama_file)"
            :src="previewUrl"
            class="w-full h-[60vh] border-0"
          ></iframe>

          <!-- Other file types -->
          <div v-else class="text-center p-8">
            <Icon name="file" class="w-16 h-16 mx-auto text-gray-400 mb-4" />
            <p class="text-gray-600 mb-4">Preview tidak tersedia untuk tipe file ini</p>
            <Button as-child>
              <a :href="route('admin-daerah.upload-dokumen.download', selectedDocument?.id_dokumen || 0)" target="_blank">
                <Icon name="download" class="w-4 h-4 mr-2" />
                Download File
              </a>
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>

    <!-- Delete Confirmation Dialog -->
    <Dialog v-model:open="showDeleteDialog">
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Konfirmasi Hapus</DialogTitle>
          <DialogDescription>
            Apakah Anda yakin ingin menghapus dokumen "{{ selectedDocument?.nama_file }}"?
            Tindakan ini tidak dapat dibatalkan.
          </DialogDescription>
        </DialogHeader>
        <div class="flex justify-end gap-2 mt-4">
          <Button variant="outline" @click="showDeleteDialog = false">
            Batal
          </Button>
          <Button
            variant="destructive"
            @click="deleteDocument"
            :disabled="deleteForm.processing"
          >
            <Icon name="trash" class="w-4 h-4 mr-2" />
            {{ deleteForm.processing ? 'Menghapus...' : 'Hapus' }}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  </AppLayout>
</template>
